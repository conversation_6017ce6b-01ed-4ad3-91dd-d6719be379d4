[{"name":"hot-reloader","duration":346,"timestamp":513936803057,"id":3,"tags":{"version":"15.3.5"},"startTime":1752395669332,"traceId":"d09f67b8e21bcf6b"},{"name":"start","duration":33,"timestamp":513936806688,"id":4,"parentId":3,"tags":{},"startTime":1752395669335,"traceId":"d09f67b8e21bcf6b"},{"name":"get-version-info","duration":1668554,"timestamp":513936807057,"id":5,"parentId":4,"tags":{},"startTime":1752395669336,"traceId":"d09f67b8e21bcf6b"},{"name":"clean","duration":21110,"timestamp":513938475883,"id":6,"parentId":4,"tags":{},"startTime":1752395671004,"traceId":"d09f67b8e21bcf6b"},{"name":"create-pages-mapping","duration":634,"timestamp":513938503593,"id":8,"parentId":7,"tags":{},"startTime":1752395671032,"traceId":"d09f67b8e21bcf6b"},{"name":"create-entrypoints","duration":74829,"timestamp":513938504353,"id":9,"parentId":7,"tags":{},"startTime":1752395671033,"traceId":"d09f67b8e21bcf6b"},{"name":"generate-webpack-config","duration":710565,"timestamp":513938579304,"id":10,"parentId":7,"tags":{},"startTime":1752395671108,"traceId":"d09f67b8e21bcf6b"},{"name":"get-webpack-config","duration":786834,"timestamp":513938503120,"id":7,"parentId":4,"tags":{},"startTime":1752395671032,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":6489,"timestamp":513939726269,"id":12,"parentId":11,"tags":{},"startTime":1752395672255,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2384,"timestamp":513939742881,"id":14,"parentId":13,"tags":{},"startTime":1752395672271,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":365,"timestamp":513939747283,"id":16,"parentId":13,"tags":{},"startTime":1752395672276,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":1243,"timestamp":513939748480,"id":17,"parentId":13,"tags":{},"startTime":1752395672277,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":691,"timestamp":513939750333,"id":18,"parentId":13,"tags":{},"startTime":1752395672279,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":754,"timestamp":513939752030,"id":19,"parentId":13,"tags":{},"startTime":1752395672281,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":7144,"timestamp":513939746295,"id":15,"parentId":13,"tags":{},"startTime":1752395672275,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":217,"timestamp":513939756116,"id":20,"parentId":13,"tags":{},"startTime":1752395672285,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":550,"timestamp":513939756428,"id":21,"parentId":13,"tags":{},"startTime":1752395672285,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":2008,"timestamp":513939757612,"id":22,"parentId":13,"tags":{},"startTime":1752395672286,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":506,"timestamp":513939759610,"id":23,"parentId":13,"tags":{},"startTime":1752395672288,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":453,"timestamp":513939759914,"id":24,"parentId":13,"tags":{},"startTime":1752395672288,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":648,"timestamp":513939760453,"id":25,"parentId":13,"tags":{},"startTime":1752395672289,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":7359,"timestamp":513939995658,"id":27,"parentId":11,"tags":{},"startTime":1752395672524,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":9903,"timestamp":513939993267,"id":26,"parentId":11,"tags":{},"startTime":1752395672522,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":265991,"timestamp":513939741931,"id":13,"parentId":11,"tags":{},"startTime":1752395672270,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":309618,"timestamp":513939699109,"id":11,"parentId":3,"tags":{"name":"client"},"startTime":1752395672228,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":58672,"timestamp":513940010264,"id":28,"parentId":3,"tags":{},"startTime":1752395672539,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":3308,"timestamp":513940101164,"id":30,"parentId":29,"tags":{},"startTime":1752395672630,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":56,"timestamp":513940105275,"id":32,"parentId":31,"tags":{},"startTime":1752395672634,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":11,"timestamp":513940105386,"id":34,"parentId":31,"tags":{},"startTime":1752395672634,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":1802,"timestamp":513940105520,"id":35,"parentId":31,"tags":{},"startTime":1752395672634,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":29,"timestamp":513940107478,"id":36,"parentId":31,"tags":{},"startTime":1752395672636,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":19,"timestamp":513940107641,"id":37,"parentId":31,"tags":{},"startTime":1752395672636,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2414,"timestamp":513940105372,"id":33,"parentId":31,"tags":{},"startTime":1752395672634,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":21,"timestamp":513940108091,"id":38,"parentId":31,"tags":{},"startTime":1752395672637,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":17,"timestamp":513940108140,"id":39,"parentId":31,"tags":{},"startTime":1752395672637,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":132,"timestamp":513940108232,"id":40,"parentId":31,"tags":{},"startTime":1752395672637,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":97,"timestamp":513940108364,"id":41,"parentId":31,"tags":{},"startTime":1752395672637,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":36,"timestamp":513940108440,"id":42,"parentId":31,"tags":{},"startTime":1752395672637,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":37,"timestamp":513940108491,"id":43,"parentId":31,"tags":{},"startTime":1752395672637,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":6149,"timestamp":513940105174,"id":31,"parentId":29,"tags":{},"startTime":1752395672634,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":14970,"timestamp":513940096736,"id":29,"parentId":3,"tags":{"name":"server"},"startTime":1752395672625,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":24851,"timestamp":513940111929,"id":44,"parentId":3,"tags":{},"startTime":1752395672640,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":657,"timestamp":513940151621,"id":46,"parentId":45,"tags":{},"startTime":1752395672680,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":59,"timestamp":513940153667,"id":48,"parentId":47,"tags":{},"startTime":1752395672682,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":11,"timestamp":513940153784,"id":50,"parentId":47,"tags":{},"startTime":1752395672682,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":34,"timestamp":513940153970,"id":51,"parentId":47,"tags":{},"startTime":1752395672682,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":14,"timestamp":513940154048,"id":52,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":12,"timestamp":513940154104,"id":53,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":390,"timestamp":513940153769,"id":49,"parentId":47,"tags":{},"startTime":1752395672682,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":41,"timestamp":513940154387,"id":54,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":21,"timestamp":513940154466,"id":55,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":182,"timestamp":513940154560,"id":56,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":59,"timestamp":513940154741,"id":57,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":26,"timestamp":513940154780,"id":58,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":29,"timestamp":513940154816,"id":59,"parentId":47,"tags":{},"startTime":1752395672683,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":2882,"timestamp":513940153578,"id":47,"parentId":45,"tags":{},"startTime":1752395672682,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":11614,"timestamp":513940144947,"id":45,"parentId":3,"tags":{"name":"edge-server"},"startTime":1752395672673,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":14519,"timestamp":513940156658,"id":60,"parentId":3,"tags":{},"startTime":1752395672685,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"make","duration":2968,"timestamp":513940502238,"id":65,"parentId":64,"tags":{},"startTime":1752395673031,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":109,"timestamp":513940506914,"id":67,"parentId":66,"tags":{},"startTime":1752395673035,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":19,"timestamp":513940507142,"id":69,"parentId":66,"tags":{},"startTime":1752395673036,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":35,"timestamp":513940507222,"id":70,"parentId":66,"tags":{},"startTime":1752395673036,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":180,"timestamp":513940507382,"id":71,"parentId":66,"tags":{},"startTime":1752395673036,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":65,"timestamp":513940507768,"id":72,"parentId":66,"tags":{},"startTime":1752395673036,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":934,"timestamp":513940507111,"id":68,"parentId":66,"tags":{},"startTime":1752395673036,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":587,"timestamp":513940509993,"id":73,"parentId":66,"tags":{},"startTime":1752395673038,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":259,"timestamp":513940510992,"id":74,"parentId":66,"tags":{},"startTime":1752395673039,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":735,"timestamp":513940518498,"id":75,"parentId":66,"tags":{},"startTime":1752395673047,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":367,"timestamp":513940519222,"id":76,"parentId":66,"tags":{},"startTime":1752395673048,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":114,"timestamp":513940519524,"id":77,"parentId":66,"tags":{},"startTime":1752395673048,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":109,"timestamp":513940519675,"id":78,"parentId":66,"tags":{},"startTime":1752395673048,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":2230,"timestamp":513940522099,"id":80,"parentId":64,"tags":{},"startTime":1752395673051,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":2650,"timestamp":513940521784,"id":79,"parentId":64,"tags":{},"startTime":1752395673050,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":18880,"timestamp":513940506660,"id":66,"parentId":64,"tags":{},"startTime":1752395673035,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":33034,"timestamp":513940492651,"id":64,"parentId":61,"tags":{"name":"client"},"startTime":1752395673021,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":72560,"timestamp":513940525807,"id":81,"parentId":61,"tags":{},"startTime":1752395673054,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":135181,"timestamp":513940473826,"id":61,"parentId":3,"tags":{"trigger":"manual"},"startTime":1752395673002,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":8781,"timestamp":513940632262,"id":83,"parentId":82,"tags":{},"startTime":1752395673161,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":305,"timestamp":513940642753,"id":85,"parentId":84,"tags":{},"startTime":1752395673171,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":41,"timestamp":513940643300,"id":87,"parentId":84,"tags":{},"startTime":1752395673172,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":327,"timestamp":513940644071,"id":88,"parentId":84,"tags":{},"startTime":1752395673173,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":63,"timestamp":513940644626,"id":89,"parentId":84,"tags":{},"startTime":1752395673173,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":168,"timestamp":513940646341,"id":90,"parentId":84,"tags":{},"startTime":1752395673175,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5640,"timestamp":513940643178,"id":86,"parentId":84,"tags":{},"startTime":1752395673172,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":160,"timestamp":513940653557,"id":91,"parentId":84,"tags":{},"startTime":1752395673182,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":416,"timestamp":513940654353,"id":92,"parentId":84,"tags":{},"startTime":1752395673183,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":1390,"timestamp":513940655340,"id":93,"parentId":84,"tags":{},"startTime":1752395673184,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":337,"timestamp":513940656719,"id":94,"parentId":84,"tags":{},"startTime":1752395673185,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":119,"timestamp":513940656988,"id":95,"parentId":84,"tags":{},"startTime":1752395673185,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":466,"timestamp":513940657137,"id":96,"parentId":84,"tags":{},"startTime":1752395673186,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":20087,"timestamp":513940642518,"id":84,"parentId":82,"tags":{},"startTime":1752395673171,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":40268,"timestamp":513940622810,"id":82,"parentId":62,"tags":{"name":"server"},"startTime":1752395673151,"traceId":"d09f67b8e21bcf6b"},{"name":"setup-dev-bundler","duration":5117284,"timestamp":513935988465,"id":2,"parentId":1,"tags":{},"startTime":1752395668517,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":472741,"timestamp":513940663248,"id":97,"parentId":62,"tags":{},"startTime":1752395673192,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":666344,"timestamp":513940474222,"id":62,"parentId":3,"tags":{"trigger":"manual"},"startTime":1752395673003,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":1498,"timestamp":513941168309,"id":99,"parentId":98,"tags":{},"startTime":1752395673697,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":224,"timestamp":513941171926,"id":101,"parentId":100,"tags":{},"startTime":1752395673700,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":112,"timestamp":513941172434,"id":103,"parentId":100,"tags":{},"startTime":1752395673701,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":65,"timestamp":513941172670,"id":104,"parentId":100,"tags":{},"startTime":1752395673701,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":53,"timestamp":513941172857,"id":105,"parentId":100,"tags":{},"startTime":1752395673701,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":71,"timestamp":513941173165,"id":106,"parentId":100,"tags":{},"startTime":1752395673702,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":1027,"timestamp":513941172300,"id":102,"parentId":100,"tags":{},"startTime":1752395673701,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":32,"timestamp":513941174144,"id":107,"parentId":100,"tags":{},"startTime":1752395673703,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":27,"timestamp":513941174236,"id":108,"parentId":100,"tags":{},"startTime":1752395673703,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":447,"timestamp":513941174385,"id":109,"parentId":100,"tags":{},"startTime":1752395673703,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":329,"timestamp":513941174816,"id":110,"parentId":100,"tags":{},"startTime":1752395673703,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":106,"timestamp":513941175074,"id":111,"parentId":100,"tags":{},"startTime":1752395673704,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":107,"timestamp":513941175218,"id":112,"parentId":100,"tags":{},"startTime":1752395673704,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":19826,"timestamp":513941171239,"id":100,"parentId":98,"tags":{},"startTime":1752395673700,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":35392,"timestamp":513941156396,"id":98,"parentId":63,"tags":{"name":"edge-server"},"startTime":1752395673685,"traceId":"d09f67b8e21bcf6b"},{"name":"run-instrumentation-hook","duration":124,"timestamp":513941489908,"id":114,"parentId":1,"tags":{},"startTime":1752395674018,"traceId":"d09f67b8e21bcf6b"},{"name":"start-dev-server","duration":7559024,"timestamp":513934004548,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18003521536","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"179113984","memory.heapTotal":"116654080","memory.heapUsed":"85895312"},"startTime":1752395666533,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":394618,"timestamp":513941191948,"id":113,"parentId":63,"tags":{},"startTime":1752395673720,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-edge-server","duration":1114926,"timestamp":513940474299,"id":63,"parentId":3,"tags":{"trigger":"manual"},"startTime":1752395673003,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"build-module-tsx","duration":157422,"timestamp":513962846649,"id":122,"parentId":119,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Contact.tsx","layer":"rsc"},"startTime":1752395695375,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":1990949,"timestamp":513961024154,"id":121,"parentId":120,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395693553,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":210077,"timestamp":513963192384,"id":130,"parentId":119,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Contact.tsx","layer":"ssr"},"startTime":1752395695721,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":2917558,"timestamp":513961004403,"id":120,"parentId":119,"tags":{},"startTime":1752395693533,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":40979,"timestamp":513963981302,"id":134,"parentId":133,"tags":{},"startTime":1752395696510,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":177,"timestamp":513964022904,"id":136,"parentId":133,"tags":{},"startTime":1752395696551,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":28578,"timestamp":513964023433,"id":137,"parentId":133,"tags":{},"startTime":1752395696552,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":216,"timestamp":513964052425,"id":138,"parentId":133,"tags":{},"startTime":1752395696581,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":183,"timestamp":513964053168,"id":139,"parentId":133,"tags":{},"startTime":1752395696582,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":37012,"timestamp":513964022665,"id":135,"parentId":133,"tags":{},"startTime":1752395696551,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":52106,"timestamp":513964086853,"id":140,"parentId":133,"tags":{},"startTime":1752395696615,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":49175,"timestamp":513964139147,"id":141,"parentId":133,"tags":{},"startTime":1752395696668,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":30942,"timestamp":513964216966,"id":142,"parentId":133,"tags":{},"startTime":1752395696745,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":1583,"timestamp":513964247893,"id":143,"parentId":133,"tags":{},"startTime":1752395696776,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":1409,"timestamp":513964249057,"id":144,"parentId":133,"tags":{},"startTime":1752395696778,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":58413,"timestamp":513964250572,"id":145,"parentId":133,"tags":{},"startTime":1752395696779,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":355174,"timestamp":513963974082,"id":133,"parentId":119,"tags":{},"startTime":1752395696503,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":3356450,"timestamp":513960999114,"id":119,"parentId":117,"tags":{"name":"server"},"startTime":1752395693528,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":53057,"timestamp":513964355821,"id":146,"parentId":117,"tags":{},"startTime":1752395696884,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":3434181,"timestamp":513960982089,"id":117,"parentId":3,"tags":{"trigger":"manual"},"startTime":1752395693511,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":1008477,"timestamp":513964458172,"id":149,"parentId":148,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395696987,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":961493,"timestamp":513965628934,"id":154,"parentId":147,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Contact.tsx","layer":"app-pages-browser"},"startTime":1752395698157,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":2144880,"timestamp":513964459294,"id":152,"parentId":148,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395696988,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":499294,"timestamp":513968719975,"id":158,"parentId":157,"tags":{},"startTime":1752395701248,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":2474638,"timestamp":513966751381,"id":157,"parentId":156,"tags":{},"startTime":1752395699280,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":265064,"timestamp":513969226887,"id":159,"parentId":156,"tags":{"astUsed":"true"},"startTime":1752395701755,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":5059938,"timestamp":513964459633,"id":153,"parentId":148,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395696988,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":5061005,"timestamp":513964458710,"id":150,"parentId":148,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395696987,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":2910387,"timestamp":513966610063,"id":156,"parentId":155,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395699139,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":3936314,"timestamp":513965641469,"id":155,"parentId":147,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395698170,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":750,"timestamp":513969579105,"id":160,"parentId":155,"tags":{},"startTime":1752395702108,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":5121430,"timestamp":513964458999,"id":151,"parentId":148,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395696987,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":5151657,"timestamp":513964429230,"id":148,"parentId":147,"tags":{},"startTime":1752395696958,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":25168,"timestamp":513969608331,"id":162,"parentId":161,"tags":{},"startTime":1752395702137,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":52,"timestamp":513969633751,"id":164,"parentId":161,"tags":{},"startTime":1752395702162,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":620,"timestamp":513969634230,"id":165,"parentId":161,"tags":{},"startTime":1752395702163,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":82,"timestamp":513969635215,"id":166,"parentId":161,"tags":{},"startTime":1752395702164,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":72,"timestamp":513969635700,"id":167,"parentId":161,"tags":{},"startTime":1752395702164,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":7356,"timestamp":513969633679,"id":163,"parentId":161,"tags":{},"startTime":1752395702162,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":41619,"timestamp":513969668996,"id":168,"parentId":161,"tags":{},"startTime":1752395702197,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":30052,"timestamp":513969710857,"id":169,"parentId":161,"tags":{},"startTime":1752395702239,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":43042,"timestamp":513969758827,"id":170,"parentId":161,"tags":{},"startTime":1752395702287,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":1062,"timestamp":513969801861,"id":171,"parentId":161,"tags":{},"startTime":1752395702330,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":1047,"timestamp":513969802824,"id":172,"parentId":161,"tags":{},"startTime":1752395702331,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":43317,"timestamp":513969803950,"id":173,"parentId":161,"tags":{},"startTime":1752395702332,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":4523,"timestamp":513969871809,"id":175,"parentId":147,"tags":{},"startTime":1752395702400,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":7290,"timestamp":513969869125,"id":174,"parentId":147,"tags":{},"startTime":1752395702398,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":297596,"timestamp":513969602558,"id":161,"parentId":147,"tags":{},"startTime":1752395702131,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":5473427,"timestamp":513964427077,"id":147,"parentId":129,"tags":{"name":"client"},"startTime":1752395696956,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":92350,"timestamp":513969900807,"id":176,"parentId":129,"tags":{},"startTime":1752395702429,"traceId":"d09f67b8e21bcf6b"},{"name":"compile-path","duration":9023410,"timestamp":513960982301,"id":118,"tags":{"trigger":"/"},"startTime":1752395693511,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":6914565,"timestamp":513963097807,"id":129,"parentId":3,"tags":{"trigger":"manual"},"startTime":1752395695626,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":9056759,"timestamp":513960959320,"id":116,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395693488,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":6556903,"timestamp":513963459461,"id":132,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395695988,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":19865,"timestamp":513970047762,"id":177,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395702576,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":19851,"timestamp":513970047890,"id":178,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395702576,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":10924770,"timestamp":513960928318,"id":115,"tags":{"url":"/"},"startTime":1752395693457,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":25,"timestamp":513971853467,"id":179,"parentId":115,"tags":{"url":"/","memory.rss":"397139968","memory.heapUsed":"196404216","memory.heapTotal":"225775616"},"startTime":1752395704382,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":9438408,"timestamp":513963412879,"id":131,"tags":{"url":"/"},"startTime":1752395695941,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":27,"timestamp":513972851564,"id":180,"parentId":131,"tags":{"url":"/","memory.rss":"420417536","memory.heapUsed":"176500928","memory.heapTotal":"235823104"},"startTime":1752395705380,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":52,"timestamp":513975305204,"id":181,"parentId":3,"tags":{},"startTime":1752395707834,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":29575,"timestamp":514044582697,"id":187,"parentId":184,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"rsc"},"startTime":1752395777114,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":101824,"timestamp":514044530603,"id":186,"parentId":185,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395777062,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":40842,"timestamp":514044706932,"id":194,"parentId":184,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"ssr"},"startTime":1752395777239,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":273365,"timestamp":514044511882,"id":185,"parentId":184,"tags":{},"startTime":1752395777044,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":13864,"timestamp":514045010186,"id":196,"parentId":195,"tags":{},"startTime":1752395777542,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":158,"timestamp":514045024812,"id":198,"parentId":195,"tags":{},"startTime":1752395777556,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":12447,"timestamp":514045025320,"id":199,"parentId":195,"tags":{},"startTime":1752395777557,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":156,"timestamp":514045038163,"id":200,"parentId":195,"tags":{},"startTime":1752395777570,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":142,"timestamp":514045038592,"id":201,"parentId":195,"tags":{},"startTime":1752395777570,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":19141,"timestamp":514045024600,"id":197,"parentId":195,"tags":{},"startTime":1752395777556,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":4533,"timestamp":514045055194,"id":202,"parentId":195,"tags":{},"startTime":1752395777587,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":14217,"timestamp":514045060225,"id":203,"parentId":195,"tags":{},"startTime":1752395777592,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":13746,"timestamp":514045088090,"id":204,"parentId":195,"tags":{},"startTime":1752395777620,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":949,"timestamp":514045101826,"id":205,"parentId":195,"tags":{},"startTime":1752395777634,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":540,"timestamp":514045102638,"id":206,"parentId":195,"tags":{},"startTime":1752395777634,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":12370,"timestamp":514045103236,"id":207,"parentId":195,"tags":{},"startTime":1752395777635,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":125853,"timestamp":514045004682,"id":195,"parentId":184,"tags":{},"startTime":1752395777536,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":657586,"timestamp":514044494435,"id":184,"parentId":182,"tags":{"name":"server"},"startTime":1752395777026,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":48518,"timestamp":514045152851,"id":208,"parentId":182,"tags":{},"startTime":1752395777685,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":758968,"timestamp":514044448372,"id":182,"parentId":3,"tags":{"trigger":"src/components/Services.tsx"},"startTime":1752395776980,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":23363,"timestamp":514045265458,"id":217,"parentId":209,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"app-pages-browser"},"startTime":1752395777797,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":50373,"timestamp":514045239080,"id":211,"parentId":210,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395777771,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":63622,"timestamp":514045239401,"id":214,"parentId":210,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395777771,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":64982,"timestamp":514045239425,"id":215,"parentId":210,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395777771,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":73028,"timestamp":514045304602,"id":220,"parentId":219,"tags":{},"startTime":1752395777836,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":74512,"timestamp":514045304516,"id":219,"parentId":218,"tags":{},"startTime":1752395777836,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":51705,"timestamp":514045379220,"id":221,"parentId":218,"tags":{"astUsed":"true"},"startTime":1752395777911,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":197445,"timestamp":514045239325,"id":212,"parentId":210,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395777771,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":140385,"timestamp":514045296851,"id":218,"parentId":216,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395777829,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":189432,"timestamp":514045260682,"id":216,"parentId":209,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395777792,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":93,"timestamp":514045450408,"id":222,"parentId":216,"tags":{},"startTime":1752395777982,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":211170,"timestamp":514045239374,"id":213,"parentId":210,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395777771,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":220102,"timestamp":514045230504,"id":210,"parentId":209,"tags":{},"startTime":1752395777762,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":7386,"timestamp":514045525675,"id":224,"parentId":223,"tags":{},"startTime":1752395778057,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":16,"timestamp":514045533235,"id":226,"parentId":223,"tags":{},"startTime":1752395778065,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":214,"timestamp":514045533472,"id":227,"parentId":223,"tags":{},"startTime":1752395778065,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":21,"timestamp":514045533805,"id":228,"parentId":223,"tags":{},"startTime":1752395778065,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":22,"timestamp":514045533929,"id":229,"parentId":223,"tags":{},"startTime":1752395778066,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":14985,"timestamp":514045533203,"id":225,"parentId":223,"tags":{},"startTime":1752395778065,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":849,"timestamp":514045552108,"id":230,"parentId":223,"tags":{},"startTime":1752395778084,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3977,"timestamp":514045553020,"id":231,"parentId":223,"tags":{},"startTime":1752395778085,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":20541,"timestamp":514045566730,"id":232,"parentId":223,"tags":{},"startTime":1752395778098,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":888,"timestamp":514045587263,"id":233,"parentId":223,"tags":{},"startTime":1752395778119,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":412,"timestamp":514045588068,"id":234,"parentId":223,"tags":{},"startTime":1752395778120,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":9063,"timestamp":514045588507,"id":235,"parentId":223,"tags":{},"startTime":1752395778120,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":2278,"timestamp":514045611635,"id":237,"parentId":209,"tags":{},"startTime":1752395778143,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":4101,"timestamp":514045609891,"id":236,"parentId":209,"tags":{},"startTime":1752395778142,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":99767,"timestamp":514045520555,"id":223,"parentId":209,"tags":{},"startTime":1752395778052,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":396252,"timestamp":514045224271,"id":209,"parentId":183,"tags":{"name":"client"},"startTime":1752395777756,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":35842,"timestamp":514045620653,"id":238,"parentId":183,"tags":{},"startTime":1752395778152,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":1216490,"timestamp":514044449591,"id":183,"parentId":3,"tags":{"trigger":"src/components/Services.tsx"},"startTime":1752395776981,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":8376,"timestamp":514045691188,"id":240,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395778223,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":32,"timestamp":514045709834,"id":242,"parentId":3,"tags":{},"startTime":1752395778242,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":5695,"timestamp":514045706344,"id":241,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395778238,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":463440,"timestamp":514045681788,"id":239,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395778213,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":6,"timestamp":514046145376,"id":243,"parentId":239,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"373575680","memory.heapUsed":"179798736","memory.heapTotal":"218550272"},"startTime":1752395778677,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":1766000,"timestamp":514044452134,"id":244,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Services.tsx"],"page":"/","isPageHidden":true},"startTime":1752395778752,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":18336,"timestamp":514048265391,"id":255,"parentId":247,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"app-pages-browser"},"startTime":1752395780797,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":35096,"timestamp":514048248815,"id":249,"parentId":248,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395780780,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":41370,"timestamp":514048249104,"id":252,"parentId":248,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395780781,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":43237,"timestamp":514048249127,"id":253,"parentId":248,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395780781,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":38625,"timestamp":514048294943,"id":258,"parentId":257,"tags":{},"startTime":1752395780827,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":39237,"timestamp":514048294863,"id":257,"parentId":256,"tags":{},"startTime":1752395780827,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":26025,"timestamp":514048334169,"id":259,"parentId":256,"tags":{"astUsed":"true"},"startTime":1752395780866,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":115374,"timestamp":514048249020,"id":250,"parentId":248,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395780781,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":77758,"timestamp":514048287231,"id":256,"parentId":254,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395780819,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":105776,"timestamp":514048264168,"id":254,"parentId":247,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395780796,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":51,"timestamp":514048370176,"id":260,"parentId":254,"tags":{},"startTime":1752395780902,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":121183,"timestamp":514048249070,"id":251,"parentId":248,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395780781,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":123056,"timestamp":514048247229,"id":248,"parentId":247,"tags":{},"startTime":1752395780779,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":7357,"timestamp":514048375663,"id":262,"parentId":261,"tags":{},"startTime":1752395780907,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":41,"timestamp":514048383210,"id":264,"parentId":261,"tags":{},"startTime":1752395780915,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":224,"timestamp":514048383506,"id":265,"parentId":261,"tags":{},"startTime":1752395780915,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":32,"timestamp":514048383812,"id":266,"parentId":261,"tags":{},"startTime":1752395780915,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":35,"timestamp":514048383919,"id":267,"parentId":261,"tags":{},"startTime":1752395780916,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2272,"timestamp":514048383146,"id":263,"parentId":261,"tags":{},"startTime":1752395780915,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":951,"timestamp":514048388224,"id":268,"parentId":261,"tags":{},"startTime":1752395780920,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":8035,"timestamp":514048389299,"id":269,"parentId":261,"tags":{},"startTime":1752395780921,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3913,"timestamp":514048400255,"id":270,"parentId":261,"tags":{},"startTime":1752395780932,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":160,"timestamp":514048404166,"id":271,"parentId":261,"tags":{},"startTime":1752395780936,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":164,"timestamp":514048404311,"id":272,"parentId":261,"tags":{},"startTime":1752395780936,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":10075,"timestamp":514048404486,"id":273,"parentId":261,"tags":{},"startTime":1752395780936,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":74,"timestamp":514048415986,"id":275,"parentId":247,"tags":{},"startTime":1752395780948,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":164,"timestamp":514048415909,"id":274,"parentId":247,"tags":{},"startTime":1752395780948,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":45554,"timestamp":514048372248,"id":261,"parentId":247,"tags":{},"startTime":1752395780904,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":171450,"timestamp":514048246407,"id":247,"parentId":245,"tags":{"name":"client"},"startTime":1752395780778,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":19681,"timestamp":514048417895,"id":276,"parentId":245,"tags":{},"startTime":1752395780950,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":215229,"timestamp":514048224244,"id":245,"parentId":3,"tags":{"trigger":"src/components/Services.tsx"},"startTime":1752395780756,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":46,"timestamp":514048447380,"id":279,"parentId":3,"tags":{},"startTime":1752395780979,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":5766,"timestamp":514048465758,"id":281,"parentId":277,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"rsc"},"startTime":1752395780997,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":30439,"timestamp":514048451941,"id":280,"parentId":278,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395780984,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":18554,"timestamp":514048512150,"id":288,"parentId":277,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"ssr"},"startTime":1752395781044,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":104236,"timestamp":514048444380,"id":278,"parentId":277,"tags":{},"startTime":1752395780976,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2752,"timestamp":514048562881,"id":290,"parentId":289,"tags":{},"startTime":1752395781095,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":20,"timestamp":514048565738,"id":292,"parentId":289,"tags":{},"startTime":1752395781097,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2963,"timestamp":514048565980,"id":293,"parentId":289,"tags":{},"startTime":1752395781098,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":28,"timestamp":514048569039,"id":294,"parentId":289,"tags":{},"startTime":1752395781101,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":16,"timestamp":514048569107,"id":295,"parentId":289,"tags":{},"startTime":1752395781101,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5580,"timestamp":514048565708,"id":291,"parentId":289,"tags":{},"startTime":1752395781097,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1846,"timestamp":514048576633,"id":296,"parentId":289,"tags":{},"startTime":1752395781108,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":8460,"timestamp":514048578617,"id":297,"parentId":289,"tags":{},"startTime":1752395781110,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":6371,"timestamp":514048595697,"id":298,"parentId":289,"tags":{},"startTime":1752395781127,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":510,"timestamp":514048602059,"id":299,"parentId":289,"tags":{},"startTime":1752395781134,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":378,"timestamp":514048602500,"id":300,"parentId":289,"tags":{},"startTime":1752395781134,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":7310,"timestamp":514048602921,"id":301,"parentId":289,"tags":{},"startTime":1752395781135,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":369000,"timestamp":514048226178,"id":302,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Services.tsx"],"page":"/","isPageHidden":true},"startTime":1752395781146,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":56456,"timestamp":514048560484,"id":289,"parentId":277,"tags":{},"startTime":1752395781092,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":191001,"timestamp":514048443757,"id":277,"parentId":246,"tags":{"name":"server"},"startTime":1752395780975,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":46968,"timestamp":514048635934,"id":303,"parentId":246,"tags":{},"startTime":1752395781168,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":460155,"timestamp":514048225538,"id":246,"parentId":3,"tags":{"trigger":"src/components/Services.tsx"},"startTime":1752395780757,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":5379,"timestamp":514048712268,"id":305,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395781244,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4502,"timestamp":514048731337,"id":306,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395781263,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":421336,"timestamp":514048708166,"id":304,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395781240,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":10,"timestamp":514049129642,"id":307,"parentId":304,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"398671872","memory.heapUsed":"200465880","memory.heapTotal":"230506496"},"startTime":1752395781661,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":14755,"timestamp":514140956548,"id":313,"parentId":310,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Contact.tsx","layer":"rsc"},"startTime":1752395873488,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":50316,"timestamp":514140927225,"id":312,"parentId":311,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395873459,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":30092,"timestamp":514141000246,"id":320,"parentId":310,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Contact.tsx","layer":"ssr"},"startTime":1752395873532,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":126961,"timestamp":514140915783,"id":311,"parentId":310,"tags":{},"startTime":1752395873447,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":6947,"timestamp":514141147263,"id":322,"parentId":321,"tags":{},"startTime":1752395873679,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":81,"timestamp":514141154675,"id":324,"parentId":321,"tags":{},"startTime":1752395873686,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":5758,"timestamp":514141155015,"id":325,"parentId":321,"tags":{},"startTime":1752395873686,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":232,"timestamp":514141161313,"id":326,"parentId":321,"tags":{},"startTime":1752395873693,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":153,"timestamp":514141162458,"id":327,"parentId":321,"tags":{},"startTime":1752395873694,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":14355,"timestamp":514141154525,"id":323,"parentId":321,"tags":{},"startTime":1752395873686,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1896,"timestamp":514141176604,"id":328,"parentId":321,"tags":{},"startTime":1752395873708,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":13394,"timestamp":514141178714,"id":329,"parentId":321,"tags":{},"startTime":1752395873710,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":14666,"timestamp":514141205780,"id":330,"parentId":321,"tags":{},"startTime":1752395873737,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":620,"timestamp":514141220440,"id":331,"parentId":321,"tags":{},"startTime":1752395873752,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":263,"timestamp":514141221014,"id":332,"parentId":321,"tags":{},"startTime":1752395873752,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":15626,"timestamp":514141221296,"id":333,"parentId":321,"tags":{},"startTime":1752395873753,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":108847,"timestamp":514141136093,"id":321,"parentId":310,"tags":{},"startTime":1752395873668,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":348763,"timestamp":514140906007,"id":310,"parentId":308,"tags":{"name":"server"},"startTime":1752395873437,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":27521,"timestamp":514141255350,"id":334,"parentId":308,"tags":{},"startTime":1752395873787,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":412798,"timestamp":514140874058,"id":308,"parentId":3,"tags":{"trigger":"src/components/Contact.tsx"},"startTime":1752395873406,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":22254,"timestamp":514141301718,"id":337,"parentId":336,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395873833,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":28500,"timestamp":514141316651,"id":343,"parentId":335,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Contact.tsx","layer":"app-pages-browser"},"startTime":1752395873848,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":45825,"timestamp":514141302136,"id":340,"parentId":336,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395873834,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":46698,"timestamp":514141302171,"id":341,"parentId":336,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395873834,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":51430,"timestamp":514141349276,"id":346,"parentId":345,"tags":{},"startTime":1752395873881,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":53384,"timestamp":514141349179,"id":345,"parentId":344,"tags":{},"startTime":1752395873881,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":34816,"timestamp":514141403185,"id":347,"parentId":344,"tags":{"astUsed":"true"},"startTime":1752395873935,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":140341,"timestamp":514141302022,"id":338,"parentId":336,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395873833,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":115411,"timestamp":514141328005,"id":344,"parentId":342,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395873859,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":145101,"timestamp":514141313749,"id":342,"parentId":335,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395873845,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":166,"timestamp":514141459119,"id":348,"parentId":342,"tags":{},"startTime":1752395873991,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":157241,"timestamp":514141302095,"id":339,"parentId":336,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395873834,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":165344,"timestamp":514141294082,"id":336,"parentId":335,"tags":{},"startTime":1752395873826,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":18460,"timestamp":514141593218,"id":350,"parentId":349,"tags":{},"startTime":1752395874125,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":28141,"timestamp":514141611935,"id":352,"parentId":349,"tags":{},"startTime":1752395874143,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":540,"timestamp":514141640655,"id":353,"parentId":349,"tags":{},"startTime":1752395874172,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":110,"timestamp":514141641542,"id":354,"parentId":349,"tags":{},"startTime":1752395874173,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":83,"timestamp":514141641978,"id":355,"parentId":349,"tags":{},"startTime":1752395874173,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":34834,"timestamp":514141611869,"id":351,"parentId":349,"tags":{},"startTime":1752395874143,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2301,"timestamp":514141654324,"id":356,"parentId":349,"tags":{},"startTime":1752395874186,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":12348,"timestamp":514141656800,"id":357,"parentId":349,"tags":{},"startTime":1752395874188,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":20847,"timestamp":514141676443,"id":358,"parentId":349,"tags":{},"startTime":1752395874208,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":4025,"timestamp":514141697275,"id":359,"parentId":349,"tags":{},"startTime":1752395874229,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":1282,"timestamp":514141701028,"id":360,"parentId":349,"tags":{},"startTime":1752395874232,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":19889,"timestamp":514141702430,"id":361,"parentId":349,"tags":{},"startTime":1752395874234,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":7129,"timestamp":514141730691,"id":363,"parentId":335,"tags":{},"startTime":1752395874262,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":7983,"timestamp":514141729930,"id":362,"parentId":335,"tags":{},"startTime":1752395874261,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":165274,"timestamp":514141587290,"id":349,"parentId":335,"tags":{},"startTime":1752395874119,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":460823,"timestamp":514141292172,"id":335,"parentId":309,"tags":{"name":"client"},"startTime":1752395873824,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":48829,"timestamp":514141753426,"id":364,"parentId":309,"tags":{},"startTime":1752395874285,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":940456,"timestamp":514140875014,"id":309,"parentId":3,"tags":{"trigger":"src/components/Contact.tsx"},"startTime":1752395873406,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"client-success","duration":16,"timestamp":514141839637,"id":366,"parentId":3,"tags":{},"startTime":1752395874371,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":6455,"timestamp":514141840210,"id":367,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395874372,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":13191,"timestamp":514141858105,"id":368,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395874390,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":375072,"timestamp":514141833559,"id":365,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395874365,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":6,"timestamp":514142208781,"id":369,"parentId":365,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"387854336","memory.heapUsed":"190256168","memory.heapTotal":"227979264"},"startTime":1752395874740,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":1377000,"timestamp":514140877613,"id":370,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Contact.tsx"],"page":"/","isPageHidden":false},"startTime":1752395874787,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":11558,"timestamp":514187097738,"id":376,"parentId":373,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752395919629,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":45182,"timestamp":514187073802,"id":375,"parentId":374,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395919605,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":12695,"timestamp":514187148509,"id":383,"parentId":373,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752395919680,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":103033,"timestamp":514187064660,"id":374,"parentId":373,"tags":{},"startTime":1752395919596,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3779,"timestamp":514187173943,"id":385,"parentId":384,"tags":{},"startTime":1752395919705,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":21,"timestamp":514187177817,"id":387,"parentId":384,"tags":{},"startTime":1752395919709,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2585,"timestamp":514187178119,"id":388,"parentId":384,"tags":{},"startTime":1752395919710,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":22,"timestamp":514187180774,"id":389,"parentId":384,"tags":{},"startTime":1752395919712,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":19,"timestamp":514187180830,"id":390,"parentId":384,"tags":{},"startTime":1752395919712,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5320,"timestamp":514187177787,"id":386,"parentId":384,"tags":{},"startTime":1752395919709,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":899,"timestamp":514187186344,"id":391,"parentId":384,"tags":{},"startTime":1752395919718,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5463,"timestamp":514187187299,"id":392,"parentId":384,"tags":{},"startTime":1752395919719,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":5847,"timestamp":514187198251,"id":393,"parentId":384,"tags":{},"startTime":1752395919730,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":530,"timestamp":514187204087,"id":394,"parentId":384,"tags":{},"startTime":1752395919736,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":409,"timestamp":514187204546,"id":395,"parentId":384,"tags":{},"startTime":1752395919736,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":6232,"timestamp":514187205002,"id":396,"parentId":384,"tags":{},"startTime":1752395919737,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":43295,"timestamp":514187171901,"id":384,"parentId":373,"tags":{},"startTime":1752395919703,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":158614,"timestamp":514187062919,"id":373,"parentId":371,"tags":{"name":"server"},"startTime":1752395919594,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":16213,"timestamp":514187222657,"id":397,"parentId":371,"tags":{},"startTime":1752395919754,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":198794,"timestamp":514187041479,"id":371,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752395919573,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":16833,"timestamp":514187258156,"id":406,"parentId":398,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752395919790,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":23634,"timestamp":514187251878,"id":400,"parentId":399,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395919783,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":30497,"timestamp":514187252021,"id":403,"parentId":399,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395919784,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":33534,"timestamp":514187252034,"id":404,"parentId":399,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395919784,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":39261,"timestamp":514187285687,"id":409,"parentId":408,"tags":{},"startTime":1752395919817,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":39953,"timestamp":514187285652,"id":408,"parentId":407,"tags":{},"startTime":1752395919817,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":33348,"timestamp":514187325687,"id":410,"parentId":407,"tags":{"astUsed":"true"},"startTime":1752395919857,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":113171,"timestamp":514187251978,"id":401,"parentId":399,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395919784,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":84937,"timestamp":514187280807,"id":407,"parentId":405,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395919812,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":117858,"timestamp":514187257307,"id":405,"parentId":398,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395919789,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":212,"timestamp":514187380875,"id":411,"parentId":405,"tags":{},"startTime":1752395919912,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":129714,"timestamp":514187252003,"id":402,"parentId":399,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395919784,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":135766,"timestamp":514187246087,"id":399,"parentId":398,"tags":{},"startTime":1752395919778,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":27007,"timestamp":514187395888,"id":413,"parentId":412,"tags":{},"startTime":1752395919927,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":18,"timestamp":514187423049,"id":415,"parentId":412,"tags":{},"startTime":1752395919955,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":613,"timestamp":514187423480,"id":416,"parentId":412,"tags":{},"startTime":1752395919955,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":120,"timestamp":514187424586,"id":417,"parentId":412,"tags":{},"startTime":1752395919956,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":40,"timestamp":514187424873,"id":418,"parentId":412,"tags":{},"startTime":1752395919956,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":7472,"timestamp":514187423012,"id":414,"parentId":412,"tags":{},"startTime":1752395919955,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":5895,"timestamp":514187444465,"id":419,"parentId":412,"tags":{},"startTime":1752395919976,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":17489,"timestamp":514187450689,"id":420,"parentId":412,"tags":{},"startTime":1752395919982,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":20445,"timestamp":514187483022,"id":421,"parentId":412,"tags":{},"startTime":1752395920015,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":23154,"timestamp":514187503457,"id":422,"parentId":412,"tags":{},"startTime":1752395920035,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":559,"timestamp":514187526539,"id":423,"parentId":412,"tags":{},"startTime":1752395920058,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":7083,"timestamp":514187527148,"id":424,"parentId":412,"tags":{},"startTime":1752395920059,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":353,"timestamp":514187537172,"id":426,"parentId":398,"tags":{},"startTime":1752395920069,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":566,"timestamp":514187536994,"id":425,"parentId":398,"tags":{},"startTime":1752395920069,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":154622,"timestamp":514187389788,"id":412,"parentId":398,"tags":{},"startTime":1752395919921,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":299167,"timestamp":514187245357,"id":398,"parentId":372,"tags":{"name":"client"},"startTime":1752395919777,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":32465,"timestamp":514187544587,"id":427,"parentId":372,"tags":{},"startTime":1752395920076,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":540345,"timestamp":514187041774,"id":372,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752395919573,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":6797,"timestamp":514187604173,"id":429,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395920136,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":6391,"timestamp":514187619965,"id":430,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395920151,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":21,"timestamp":514188017793,"id":431,"parentId":3,"tags":{},"startTime":1752395920549,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":438679,"timestamp":514187596786,"id":428,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395920128,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":132,"timestamp":514188036365,"id":432,"parentId":428,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"383557632","memory.heapUsed":"194146632","memory.heapTotal":"218066944"},"startTime":1752395920568,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":1052000,"timestamp":514187043546,"id":433,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":false},"startTime":1752395920627,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8333,"timestamp":514200571341,"id":439,"parentId":436,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752395933103,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":35756,"timestamp":514200547858,"id":438,"parentId":437,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395933079,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8884,"timestamp":514200606927,"id":446,"parentId":436,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752395933138,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":80716,"timestamp":514200539050,"id":437,"parentId":436,"tags":{},"startTime":1752395933071,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3072,"timestamp":514200623691,"id":448,"parentId":447,"tags":{},"startTime":1752395933155,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":23,"timestamp":514200626934,"id":450,"parentId":447,"tags":{},"startTime":1752395933158,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2644,"timestamp":514200627020,"id":451,"parentId":447,"tags":{},"startTime":1752395933159,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":12,"timestamp":514200629730,"id":452,"parentId":447,"tags":{},"startTime":1752395933161,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":9,"timestamp":514200629769,"id":453,"parentId":447,"tags":{},"startTime":1752395933161,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":4524,"timestamp":514200626891,"id":449,"parentId":447,"tags":{},"startTime":1752395933158,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":795,"timestamp":514200635279,"id":454,"parentId":447,"tags":{},"startTime":1752395933167,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":2881,"timestamp":514200636181,"id":455,"parentId":447,"tags":{},"startTime":1752395933168,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3215,"timestamp":514200641876,"id":456,"parentId":447,"tags":{},"startTime":1752395933173,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":217,"timestamp":514200645088,"id":457,"parentId":447,"tags":{},"startTime":1752395933177,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":175,"timestamp":514200645282,"id":458,"parentId":447,"tags":{},"startTime":1752395933177,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":1657,"timestamp":514200645468,"id":459,"parentId":447,"tags":{},"startTime":1752395933177,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":27175,"timestamp":514200622119,"id":447,"parentId":436,"tags":{},"startTime":1752395933154,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":116086,"timestamp":514200538254,"id":436,"parentId":434,"tags":{"name":"server"},"startTime":1752395933070,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":17242,"timestamp":514200654456,"id":460,"parentId":434,"tags":{},"startTime":1752395933186,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":153367,"timestamp":514200519133,"id":434,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752395933051,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":9631,"timestamp":514200692377,"id":469,"parentId":461,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752395933224,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":17509,"timestamp":514200684622,"id":463,"parentId":462,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395933216,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":26714,"timestamp":514200684933,"id":466,"parentId":462,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395933216,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":27960,"timestamp":514200684961,"id":467,"parentId":462,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395933216,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":47838,"timestamp":514200713148,"id":472,"parentId":471,"tags":{},"startTime":1752395933245,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":48783,"timestamp":514200713075,"id":471,"parentId":470,"tags":{},"startTime":1752395933245,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":23543,"timestamp":514200761981,"id":473,"parentId":470,"tags":{"astUsed":"true"},"startTime":1752395933294,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":105916,"timestamp":514200684752,"id":464,"parentId":462,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395933216,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":86885,"timestamp":514200704382,"id":470,"parentId":468,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395933236,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":107876,"timestamp":514200691073,"id":468,"parentId":461,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395933223,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":43,"timestamp":514200799114,"id":474,"parentId":468,"tags":{},"startTime":1752395933331,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":114301,"timestamp":514200684890,"id":465,"parentId":462,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395933216,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":121708,"timestamp":514200677516,"id":462,"parentId":461,"tags":{},"startTime":1752395933209,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2956,"timestamp":514200803320,"id":476,"parentId":475,"tags":{},"startTime":1752395933335,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":20,"timestamp":514200806391,"id":478,"parentId":475,"tags":{},"startTime":1752395933338,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":195,"timestamp":514200806460,"id":479,"parentId":475,"tags":{},"startTime":1752395933338,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":18,"timestamp":514200806733,"id":480,"parentId":475,"tags":{},"startTime":1752395933338,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":19,"timestamp":514200806809,"id":481,"parentId":475,"tags":{},"startTime":1752395933338,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3556,"timestamp":514200806364,"id":477,"parentId":475,"tags":{},"startTime":1752395933338,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2184,"timestamp":514200813982,"id":482,"parentId":475,"tags":{},"startTime":1752395933346,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":8104,"timestamp":514200816333,"id":483,"parentId":475,"tags":{},"startTime":1752395933348,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":13846,"timestamp":514200853844,"id":484,"parentId":475,"tags":{},"startTime":1752395933385,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":289,"timestamp":514200867685,"id":485,"parentId":475,"tags":{},"startTime":1752395933399,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":462,"timestamp":514200867950,"id":486,"parentId":475,"tags":{},"startTime":1752395933399,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":7850,"timestamp":514200868436,"id":487,"parentId":475,"tags":{},"startTime":1752395933400,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":335,"timestamp":514200879810,"id":489,"parentId":461,"tags":{},"startTime":1752395933411,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":663,"timestamp":514200879512,"id":488,"parentId":461,"tags":{},"startTime":1752395933411,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":82020,"timestamp":514200801125,"id":475,"parentId":461,"tags":{},"startTime":1752395933333,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":206923,"timestamp":514200676320,"id":461,"parentId":435,"tags":{"name":"client"},"startTime":1752395933208,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":60103,"timestamp":514200883309,"id":490,"parentId":435,"tags":{},"startTime":1752395933415,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":431666,"timestamp":514200519431,"id":435,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752395933051,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":6219,"timestamp":514200969075,"id":492,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395933501,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":47,"timestamp":514200990738,"id":493,"parentId":3,"tags":{},"startTime":1752395933522,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":11358,"timestamp":514200991874,"id":494,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395933523,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":140185,"timestamp":514200965581,"id":491,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395933497,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":11,"timestamp":514201105928,"id":495,"parentId":491,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"399560704","memory.heapUsed":"192307664","memory.heapTotal":"239443968"},"startTime":1752395933637,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":660000,"timestamp":514200521411,"id":496,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1752395933713,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":6361,"timestamp":514212339541,"id":502,"parentId":499,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752395944871,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":31317,"timestamp":514212321529,"id":501,"parentId":500,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395944853,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":16268,"timestamp":514212378373,"id":509,"parentId":499,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752395944910,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":84222,"timestamp":514212315152,"id":500,"parentId":499,"tags":{},"startTime":1752395944847,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2374,"timestamp":514212403687,"id":511,"parentId":510,"tags":{},"startTime":1752395944935,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":7,"timestamp":514212406114,"id":513,"parentId":510,"tags":{},"startTime":1752395944938,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":1617,"timestamp":514212406143,"id":514,"parentId":510,"tags":{},"startTime":1752395944938,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":13,"timestamp":514212407825,"id":515,"parentId":510,"tags":{},"startTime":1752395944939,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":9,"timestamp":514212407866,"id":516,"parentId":510,"tags":{},"startTime":1752395944939,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3573,"timestamp":514212406100,"id":512,"parentId":510,"tags":{},"startTime":1752395944938,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":594,"timestamp":514212411020,"id":517,"parentId":510,"tags":{},"startTime":1752395944943,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3154,"timestamp":514212411664,"id":518,"parentId":510,"tags":{},"startTime":1752395944943,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3518,"timestamp":514212417478,"id":519,"parentId":510,"tags":{},"startTime":1752395944949,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":441,"timestamp":514212420984,"id":520,"parentId":510,"tags":{},"startTime":1752395944953,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":360,"timestamp":514212421376,"id":521,"parentId":510,"tags":{},"startTime":1752395944953,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":2887,"timestamp":514212421772,"id":522,"parentId":510,"tags":{},"startTime":1752395944953,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":25270,"timestamp":514212402189,"id":510,"parentId":499,"tags":{},"startTime":1752395944934,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":123238,"timestamp":514212313950,"id":499,"parentId":497,"tags":{"name":"server"},"startTime":1752395944845,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":15638,"timestamp":514212437346,"id":523,"parentId":497,"tags":{},"startTime":1752395944969,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":155722,"timestamp":514212298051,"id":497,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752395944830,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":9221,"timestamp":514212463104,"id":526,"parentId":525,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395944995,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8603,"timestamp":514212468221,"id":532,"parentId":524,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752395945000,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":17775,"timestamp":514212463323,"id":529,"parentId":525,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395944995,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":18516,"timestamp":514212463332,"id":530,"parentId":525,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395944995,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":37053,"timestamp":514212483202,"id":535,"parentId":534,"tags":{},"startTime":1752395945015,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":37880,"timestamp":514212483139,"id":534,"parentId":533,"tags":{},"startTime":1752395945015,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":18761,"timestamp":514212521106,"id":536,"parentId":533,"tags":{"astUsed":"true"},"startTime":1752395945053,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":85632,"timestamp":514212463276,"id":527,"parentId":525,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395944995,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":72102,"timestamp":514212478125,"id":533,"parentId":531,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395945010,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":88991,"timestamp":514212467412,"id":531,"parentId":524,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395944999,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":43,"timestamp":514212556607,"id":537,"parentId":531,"tags":{},"startTime":1752395945088,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":93413,"timestamp":514212463313,"id":528,"parentId":525,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395944995,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":100022,"timestamp":514212456733,"id":525,"parentId":524,"tags":{},"startTime":1752395944988,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":1140,"timestamp":514212559613,"id":539,"parentId":538,"tags":{},"startTime":1752395945091,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":5,"timestamp":514212560788,"id":541,"parentId":538,"tags":{},"startTime":1752395945092,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":50,"timestamp":514212560809,"id":542,"parentId":538,"tags":{},"startTime":1752395945092,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":6,"timestamp":514212560879,"id":543,"parentId":538,"tags":{},"startTime":1752395945092,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":5,"timestamp":514212560903,"id":544,"parentId":538,"tags":{},"startTime":1752395945092,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":1857,"timestamp":514212560779,"id":540,"parentId":538,"tags":{},"startTime":1752395945092,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":795,"timestamp":514212564540,"id":545,"parentId":538,"tags":{},"startTime":1752395945096,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3369,"timestamp":514212565390,"id":546,"parentId":538,"tags":{},"startTime":1752395945097,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3502,"timestamp":514212570521,"id":547,"parentId":538,"tags":{},"startTime":1752395945102,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":197,"timestamp":514212574021,"id":548,"parentId":538,"tags":{},"startTime":1752395945106,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":149,"timestamp":514212574204,"id":549,"parentId":538,"tags":{},"startTime":1752395945106,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":3145,"timestamp":514212574363,"id":550,"parentId":538,"tags":{},"startTime":1752395945106,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":242,"timestamp":514212579210,"id":552,"parentId":524,"tags":{},"startTime":1752395945111,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":393,"timestamp":514212579082,"id":551,"parentId":524,"tags":{},"startTime":1752395945111,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":25618,"timestamp":514212558244,"id":538,"parentId":524,"tags":{},"startTime":1752395945090,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":127623,"timestamp":514212456346,"id":524,"parentId":498,"tags":{"name":"client"},"startTime":1752395944988,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":43540,"timestamp":514212584039,"id":553,"parentId":498,"tags":{},"startTime":1752395945116,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":334022,"timestamp":514212298295,"id":498,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752395944830,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":5328,"timestamp":514212642508,"id":555,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395945174,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4775,"timestamp":514212651375,"id":556,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395945183,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":5,"timestamp":514212757544,"id":557,"parentId":3,"tags":{},"startTime":1752395945289,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":124285,"timestamp":514212640161,"id":554,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395945172,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":7,"timestamp":514212764563,"id":558,"parentId":554,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"408883200","memory.heapUsed":"209444976","memory.heapTotal":"240222208"},"startTime":1752395945296,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":532000,"timestamp":514212299070,"id":559,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1752395945364,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":5711,"timestamp":514229376064,"id":565,"parentId":562,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752395961908,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":33739,"timestamp":514229359903,"id":564,"parentId":563,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752395961891,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":18321,"timestamp":514229433208,"id":572,"parentId":562,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752395961965,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":108253,"timestamp":514229354327,"id":563,"parentId":562,"tags":{},"startTime":1752395961886,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":4329,"timestamp":514229473852,"id":574,"parentId":573,"tags":{},"startTime":1752395962005,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":8,"timestamp":514229478255,"id":576,"parentId":573,"tags":{},"startTime":1752395962010,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":3249,"timestamp":514229478292,"id":577,"parentId":573,"tags":{},"startTime":1752395962010,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":14,"timestamp":514229481631,"id":578,"parentId":573,"tags":{},"startTime":1752395962013,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":11,"timestamp":514229481683,"id":579,"parentId":573,"tags":{},"startTime":1752395962013,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":4623,"timestamp":514229478235,"id":575,"parentId":573,"tags":{},"startTime":1752395962010,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1188,"timestamp":514229485530,"id":580,"parentId":573,"tags":{},"startTime":1752395962017,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":4003,"timestamp":514229486900,"id":581,"parentId":573,"tags":{},"startTime":1752395962018,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":1879,"timestamp":514229493102,"id":582,"parentId":573,"tags":{},"startTime":1752395962025,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":202,"timestamp":514229494978,"id":583,"parentId":573,"tags":{},"startTime":1752395962027,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":176,"timestamp":514229495161,"id":584,"parentId":573,"tags":{},"startTime":1752395962027,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":17967,"timestamp":514229495353,"id":585,"parentId":573,"tags":{},"startTime":1752395962027,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":49357,"timestamp":514229469637,"id":573,"parentId":562,"tags":{},"startTime":1752395962001,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":178809,"timestamp":514229353141,"id":562,"parentId":560,"tags":{"name":"server"},"startTime":1752395961885,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":26171,"timestamp":514229532120,"id":586,"parentId":560,"tags":{},"startTime":1752395962064,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":225387,"timestamp":514229333994,"id":560,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752395961866,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8844,"timestamp":514229581542,"id":595,"parentId":587,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752395962113,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":17157,"timestamp":514229573414,"id":589,"parentId":588,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752395962105,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":20471,"timestamp":514229573651,"id":592,"parentId":588,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752395962105,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":21319,"timestamp":514229573674,"id":593,"parentId":588,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395962105,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":54213,"timestamp":514229595126,"id":598,"parentId":597,"tags":{},"startTime":1752395962127,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":55052,"timestamp":514229595078,"id":597,"parentId":596,"tags":{},"startTime":1752395962127,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":28361,"timestamp":514229650444,"id":599,"parentId":596,"tags":{"astUsed":"true"},"startTime":1752395962182,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":112467,"timestamp":514229573598,"id":590,"parentId":588,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752395962105,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":94508,"timestamp":514229592664,"id":596,"parentId":594,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752395962124,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":123901,"timestamp":514229580420,"id":594,"parentId":587,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752395962112,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":88,"timestamp":514229704723,"id":600,"parentId":594,"tags":{},"startTime":1752395962236,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":131224,"timestamp":514229573629,"id":591,"parentId":588,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752395962105,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":140942,"timestamp":514229563957,"id":588,"parentId":587,"tags":{},"startTime":1752395962095,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2383,"timestamp":514229710990,"id":602,"parentId":601,"tags":{},"startTime":1752395962243,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":12,"timestamp":514229713469,"id":604,"parentId":601,"tags":{},"startTime":1752395962245,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":124,"timestamp":514229713514,"id":605,"parentId":601,"tags":{},"startTime":1752395962245,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":14,"timestamp":514229713696,"id":606,"parentId":601,"tags":{},"startTime":1752395962245,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":12,"timestamp":514229713752,"id":607,"parentId":601,"tags":{},"startTime":1752395962245,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2702,"timestamp":514229713445,"id":603,"parentId":601,"tags":{},"startTime":1752395962245,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1253,"timestamp":514229719015,"id":608,"parentId":601,"tags":{},"startTime":1752395962251,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5445,"timestamp":514229720359,"id":609,"parentId":601,"tags":{},"startTime":1752395962252,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":10427,"timestamp":514229732914,"id":610,"parentId":601,"tags":{},"startTime":1752395962264,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":599,"timestamp":514229743322,"id":611,"parentId":601,"tags":{},"startTime":1752395962275,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":269,"timestamp":514229743889,"id":612,"parentId":601,"tags":{},"startTime":1752395962275,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":15869,"timestamp":514229744181,"id":613,"parentId":601,"tags":{},"startTime":1752395962276,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":259,"timestamp":514229762734,"id":615,"parentId":587,"tags":{},"startTime":1752395962294,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":533,"timestamp":514229762487,"id":614,"parentId":587,"tags":{},"startTime":1752395962294,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":59062,"timestamp":514229708253,"id":601,"parentId":587,"tags":{},"startTime":1752395962240,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":204617,"timestamp":514229562835,"id":587,"parentId":561,"tags":{"name":"client"},"startTime":1752395962094,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":22295,"timestamp":514229767552,"id":616,"parentId":561,"tags":{},"startTime":1752395962299,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":458549,"timestamp":514229334376,"id":561,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752395961866,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4406,"timestamp":514229801781,"id":618,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395962333,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":27,"timestamp":514229811597,"id":620,"parentId":3,"tags":{},"startTime":1752395962343,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4511,"timestamp":514229809790,"id":619,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752395962341,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":112762,"timestamp":514229799394,"id":617,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752395962331,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":6,"timestamp":514229912283,"id":621,"parentId":617,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"416878592","memory.heapUsed":"215978872","memory.heapTotal":"247332864"},"startTime":1752395962444,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":700000,"timestamp":514229336142,"id":622,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752395962569,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":39548,"timestamp":514374606429,"id":633,"parentId":625,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752396107138,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":68642,"timestamp":514374577693,"id":627,"parentId":626,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396107110,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":76737,"timestamp":514374578146,"id":630,"parentId":626,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396107110,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":77686,"timestamp":514374578155,"id":631,"parentId":626,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396107110,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":61973,"timestamp":514374656214,"id":636,"parentId":635,"tags":{},"startTime":1752396107188,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":62970,"timestamp":514374656149,"id":635,"parentId":634,"tags":{},"startTime":1752396107188,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":34303,"timestamp":514374719284,"id":637,"parentId":634,"tags":{"astUsed":"true"},"startTime":1752396107251,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":191791,"timestamp":514374578103,"id":628,"parentId":626,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396107110,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":121108,"timestamp":514374649883,"id":634,"parentId":632,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396107182,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":189129,"timestamp":514374600958,"id":632,"parentId":625,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396107133,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":132,"timestamp":514374790309,"id":638,"parentId":632,"tags":{},"startTime":1752396107322,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":212339,"timestamp":514374578135,"id":629,"parentId":626,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396107110,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":215839,"timestamp":514374574710,"id":626,"parentId":625,"tags":{},"startTime":1752396107107,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":15707,"timestamp":514374889416,"id":640,"parentId":639,"tags":{},"startTime":1752396107421,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":57,"timestamp":514374905603,"id":642,"parentId":639,"tags":{},"startTime":1752396107438,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":224,"timestamp":514374905815,"id":643,"parentId":639,"tags":{},"startTime":1752396107438,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":75,"timestamp":514374906336,"id":644,"parentId":639,"tags":{},"startTime":1752396107438,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":78,"timestamp":514374906607,"id":645,"parentId":639,"tags":{},"startTime":1752396107439,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":4669,"timestamp":514374905293,"id":641,"parentId":639,"tags":{},"startTime":1752396107437,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2886,"timestamp":514374921655,"id":646,"parentId":639,"tags":{},"startTime":1752396107454,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":36377,"timestamp":514374924773,"id":647,"parentId":639,"tags":{},"startTime":1752396107457,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":40320,"timestamp":514374980634,"id":648,"parentId":639,"tags":{},"startTime":1752396107513,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":2122,"timestamp":514375020905,"id":649,"parentId":639,"tags":{},"startTime":1752396107553,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":943,"timestamp":514375022904,"id":650,"parentId":639,"tags":{},"startTime":1752396107555,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":13899,"timestamp":514375023935,"id":651,"parentId":639,"tags":{},"startTime":1752396107556,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1717,"timestamp":514375043219,"id":653,"parentId":625,"tags":{},"startTime":1752396107575,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":2523,"timestamp":514375042478,"id":652,"parentId":625,"tags":{},"startTime":1752396107574,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":166614,"timestamp":514374883657,"id":639,"parentId":625,"tags":{},"startTime":1752396107416,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":481897,"timestamp":514374568559,"id":625,"parentId":623,"tags":{"name":"client"},"startTime":1752396107101,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":42167,"timestamp":514375050721,"id":654,"parentId":623,"tags":{},"startTime":1752396107583,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":548761,"timestamp":514374548652,"id":623,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396107081,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":22,"timestamp":514375114711,"id":657,"parentId":3,"tags":{},"startTime":1752396107647,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":18778,"timestamp":514375144139,"id":659,"parentId":655,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752396107676,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":63020,"timestamp":514375120898,"id":658,"parentId":656,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396107653,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":19456,"timestamp":514375264295,"id":666,"parentId":655,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752396107796,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":200255,"timestamp":514375106539,"id":656,"parentId":655,"tags":{},"startTime":1752396107639,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3083,"timestamp":514375462941,"id":668,"parentId":667,"tags":{},"startTime":1752396107995,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":11,"timestamp":514375466108,"id":670,"parentId":667,"tags":{},"startTime":1752396107998,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":5516,"timestamp":514375466352,"id":671,"parentId":667,"tags":{},"startTime":1752396107998,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":63,"timestamp":514375472218,"id":672,"parentId":667,"tags":{},"startTime":1752396108004,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":33,"timestamp":514375472509,"id":673,"parentId":667,"tags":{},"startTime":1752396108004,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":9601,"timestamp":514375466088,"id":669,"parentId":667,"tags":{},"startTime":1752396107998,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1773,"timestamp":514375479444,"id":674,"parentId":667,"tags":{},"startTime":1752396108011,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3837,"timestamp":514375481330,"id":675,"parentId":667,"tags":{},"startTime":1752396108013,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3975,"timestamp":514375493880,"id":676,"parentId":667,"tags":{},"startTime":1752396108026,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":286,"timestamp":514375497850,"id":677,"parentId":667,"tags":{},"startTime":1752396108030,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":197,"timestamp":514375498113,"id":678,"parentId":667,"tags":{},"startTime":1752396108030,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":3373,"timestamp":514375498325,"id":679,"parentId":667,"tags":{},"startTime":1752396108030,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":810000,"timestamp":514374550045,"id":680,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":false},"startTime":1752396108042,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":79432,"timestamp":514375436398,"id":667,"parentId":655,"tags":{},"startTime":1752396107968,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":422466,"timestamp":514375104035,"id":655,"parentId":624,"tags":{"name":"server"},"startTime":1752396107636,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":35533,"timestamp":514375526786,"id":681,"parentId":624,"tags":{},"startTime":1752396108059,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":1018070,"timestamp":514374549797,"id":624,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396107082,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":6591,"timestamp":514375586437,"id":683,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396108118,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":3503,"timestamp":514375598714,"id":684,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396108131,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":362929,"timestamp":514375579338,"id":682,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396108111,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":9,"timestamp":514375942513,"id":685,"parentId":682,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"401690624","memory.heapUsed":"208592808","memory.heapTotal":"238903296"},"startTime":1752396108474,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":6949,"timestamp":514417842365,"id":691,"parentId":688,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752396150374,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":38379,"timestamp":514417822722,"id":690,"parentId":689,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396150355,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":15667,"timestamp":514417880880,"id":698,"parentId":688,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752396150413,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":88243,"timestamp":514417817144,"id":689,"parentId":688,"tags":{},"startTime":1752396150349,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3517,"timestamp":514417909651,"id":700,"parentId":699,"tags":{},"startTime":1752396150442,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":9,"timestamp":514417913273,"id":702,"parentId":699,"tags":{},"startTime":1752396150445,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":4415,"timestamp":514417913494,"id":703,"parentId":699,"tags":{},"startTime":1752396150445,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":17,"timestamp":514417918008,"id":704,"parentId":699,"tags":{},"startTime":1752396150450,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":15,"timestamp":514417918061,"id":705,"parentId":699,"tags":{},"startTime":1752396150450,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5948,"timestamp":514417913253,"id":701,"parentId":699,"tags":{},"startTime":1752396150445,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":522,"timestamp":514417921859,"id":706,"parentId":699,"tags":{},"startTime":1752396150454,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3821,"timestamp":514417922406,"id":707,"parentId":699,"tags":{},"startTime":1752396150454,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3948,"timestamp":514417927710,"id":708,"parentId":699,"tags":{},"startTime":1752396150460,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":295,"timestamp":514417931653,"id":709,"parentId":699,"tags":{},"startTime":1752396150464,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":334,"timestamp":514417931920,"id":710,"parentId":699,"tags":{},"startTime":1752396150464,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":4852,"timestamp":514417932284,"id":711,"parentId":699,"tags":{},"startTime":1752396150464,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":30980,"timestamp":514417908359,"id":699,"parentId":688,"tags":{},"startTime":1752396150440,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":127242,"timestamp":514417816046,"id":688,"parentId":686,"tags":{"name":"server"},"startTime":1752396150348,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":12828,"timestamp":514417943341,"id":712,"parentId":686,"tags":{},"startTime":1752396150475,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":156970,"timestamp":514417799820,"id":686,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396150332,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":11687,"timestamp":514417971778,"id":721,"parentId":713,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752396150504,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":16812,"timestamp":514417966851,"id":715,"parentId":714,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396150499,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":23173,"timestamp":514417967002,"id":718,"parentId":714,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396150499,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":24116,"timestamp":514417967009,"id":719,"parentId":714,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396150499,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":35764,"timestamp":514417991225,"id":724,"parentId":723,"tags":{},"startTime":1752396150523,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":36207,"timestamp":514417991194,"id":723,"parentId":722,"tags":{},"startTime":1752396150523,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":23718,"timestamp":514418027484,"id":725,"parentId":722,"tags":{"astUsed":"true"},"startTime":1752396150559,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":91152,"timestamp":514417966977,"id":716,"parentId":714,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396150499,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":71928,"timestamp":514417986804,"id":722,"parentId":720,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396150519,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":94772,"timestamp":514417971299,"id":720,"parentId":713,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396150503,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":89,"timestamp":514418066339,"id":726,"parentId":720,"tags":{},"startTime":1752396150598,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":99477,"timestamp":514417966993,"id":717,"parentId":714,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396150499,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":106688,"timestamp":514417959834,"id":714,"parentId":713,"tags":{},"startTime":1752396150492,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":1997,"timestamp":514418070960,"id":728,"parentId":727,"tags":{},"startTime":1752396150603,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":10,"timestamp":514418073041,"id":730,"parentId":727,"tags":{},"startTime":1752396150605,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":133,"timestamp":514418073291,"id":731,"parentId":727,"tags":{},"startTime":1752396150605,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":13,"timestamp":514418073474,"id":732,"parentId":727,"tags":{},"startTime":1752396150605,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":11,"timestamp":514418073532,"id":733,"parentId":727,"tags":{},"startTime":1752396150606,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2836,"timestamp":514418073021,"id":729,"parentId":727,"tags":{},"startTime":1752396150605,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1256,"timestamp":514418082831,"id":734,"parentId":727,"tags":{},"startTime":1752396150615,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5657,"timestamp":514418084228,"id":735,"parentId":727,"tags":{},"startTime":1752396150616,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":13338,"timestamp":514418093393,"id":736,"parentId":727,"tags":{},"startTime":1752396150625,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":809,"timestamp":514418106720,"id":737,"parentId":727,"tags":{},"startTime":1752396150639,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":599,"timestamp":514418107472,"id":738,"parentId":727,"tags":{},"startTime":1752396150639,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":13170,"timestamp":514418108142,"id":739,"parentId":727,"tags":{},"startTime":1752396150640,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":157,"timestamp":514418123734,"id":741,"parentId":713,"tags":{},"startTime":1752396150656,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":341,"timestamp":514418123572,"id":740,"parentId":713,"tags":{},"startTime":1752396150656,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":59092,"timestamp":514418068783,"id":727,"parentId":713,"tags":{},"startTime":1752396150601,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":168580,"timestamp":514417959410,"id":713,"parentId":687,"tags":{"name":"client"},"startTime":1752396150491,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":31891,"timestamp":514418128044,"id":742,"parentId":687,"tags":{},"startTime":1752396150660,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":364052,"timestamp":514417800014,"id":687,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396150332,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":2998,"timestamp":514418174038,"id":744,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396150706,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4186,"timestamp":514418179881,"id":745,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396150712,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":4,"timestamp":514418290194,"id":746,"parentId":3,"tags":{},"startTime":1752396150822,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":131101,"timestamp":514418171668,"id":743,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396150704,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":8,"timestamp":514418302892,"id":747,"parentId":743,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"410968064","memory.heapUsed":"217381184","memory.heapTotal":"237154304"},"startTime":1752396150835,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":615000,"timestamp":514417801556,"id":748,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1752396150950,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":22640,"timestamp":514494201648,"id":759,"parentId":751,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752396226732,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":44328,"timestamp":514494180360,"id":753,"parentId":752,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396226711,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":52391,"timestamp":514494181134,"id":756,"parentId":752,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396226712,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":53765,"timestamp":514494181168,"id":757,"parentId":752,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396226712,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":44880,"timestamp":514494236347,"id":762,"parentId":761,"tags":{},"startTime":1752396226767,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":45280,"timestamp":514494236273,"id":761,"parentId":760,"tags":{},"startTime":1752396226767,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":26639,"timestamp":514494281621,"id":763,"parentId":760,"tags":{"astUsed":"true"},"startTime":1752396226812,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":137531,"timestamp":514494181035,"id":754,"parentId":752,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396226712,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":88815,"timestamp":514494229957,"id":760,"parentId":758,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396226760,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":127456,"timestamp":514494201054,"id":758,"parentId":751,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396226732,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":49,"timestamp":514494328694,"id":764,"parentId":758,"tags":{},"startTime":1752396226859,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":147677,"timestamp":514494181095,"id":755,"parentId":752,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396226712,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":150753,"timestamp":514494178047,"id":752,"parentId":751,"tags":{},"startTime":1752396226709,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":9034,"timestamp":514494384261,"id":766,"parentId":765,"tags":{},"startTime":1752396226915,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":40,"timestamp":514494393632,"id":768,"parentId":765,"tags":{},"startTime":1752396226924,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":146,"timestamp":514494393772,"id":769,"parentId":765,"tags":{},"startTime":1752396226924,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":36,"timestamp":514494394012,"id":770,"parentId":765,"tags":{},"startTime":1752396226925,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":37,"timestamp":514494394133,"id":771,"parentId":765,"tags":{},"startTime":1752396226925,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3301,"timestamp":514494393415,"id":767,"parentId":765,"tags":{},"startTime":1752396226924,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1579,"timestamp":514494404734,"id":772,"parentId":765,"tags":{},"startTime":1752396226935,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5926,"timestamp":514494406409,"id":773,"parentId":765,"tags":{},"startTime":1752396226937,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":9925,"timestamp":514494415392,"id":774,"parentId":765,"tags":{},"startTime":1752396226946,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":645,"timestamp":514494425313,"id":775,"parentId":765,"tags":{},"startTime":1752396226956,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":475,"timestamp":514494425928,"id":776,"parentId":765,"tags":{},"startTime":1752396226956,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":13562,"timestamp":514494426435,"id":777,"parentId":765,"tags":{},"startTime":1752396226957,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":325,"timestamp":514494446765,"id":779,"parentId":751,"tags":{},"startTime":1752396226977,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":1024,"timestamp":514494446110,"id":778,"parentId":751,"tags":{},"startTime":1752396226977,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":72871,"timestamp":514494381099,"id":765,"parentId":751,"tags":{},"startTime":1752396226912,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":277581,"timestamp":514494176563,"id":751,"parentId":749,"tags":{"name":"client"},"startTime":1752396226707,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":53041,"timestamp":514494454270,"id":780,"parentId":749,"tags":{},"startTime":1752396226985,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":352219,"timestamp":514494160818,"id":749,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396226691,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":9,"timestamp":514494526393,"id":783,"parentId":3,"tags":{},"startTime":1752396227057,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":6861,"timestamp":514494559444,"id":785,"parentId":781,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752396227090,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":40872,"timestamp":514494532365,"id":784,"parentId":782,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396227063,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8268,"timestamp":514494596650,"id":792,"parentId":781,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752396227127,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":91353,"timestamp":514494520138,"id":782,"parentId":781,"tags":{},"startTime":1752396227051,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":6068,"timestamp":514494710173,"id":794,"parentId":793,"tags":{},"startTime":1752396227241,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":111,"timestamp":514494735269,"id":796,"parentId":793,"tags":{},"startTime":1752396227266,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":5591,"timestamp":514494735790,"id":797,"parentId":793,"tags":{},"startTime":1752396227266,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":84,"timestamp":514494741729,"id":798,"parentId":793,"tags":{},"startTime":1752396227272,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":37,"timestamp":514494742061,"id":799,"parentId":793,"tags":{},"startTime":1752396227273,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":28723,"timestamp":514494716345,"id":795,"parentId":793,"tags":{},"startTime":1752396227247,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1545,"timestamp":514494749875,"id":800,"parentId":793,"tags":{},"startTime":1752396227280,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":8297,"timestamp":514494751685,"id":801,"parentId":793,"tags":{},"startTime":1752396227282,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":10318,"timestamp":514494771889,"id":802,"parentId":793,"tags":{},"startTime":1752396227302,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":701,"timestamp":514494782194,"id":803,"parentId":793,"tags":{},"startTime":1752396227313,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":519,"timestamp":514494782825,"id":804,"parentId":793,"tags":{},"startTime":1752396227313,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":14209,"timestamp":514494783408,"id":805,"parentId":793,"tags":{},"startTime":1752396227314,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":491000,"timestamp":514494163920,"id":806,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1752396227331,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":105143,"timestamp":514494701829,"id":793,"parentId":781,"tags":{},"startTime":1752396227232,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":298192,"timestamp":514494518691,"id":781,"parentId":750,"tags":{"name":"server"},"startTime":1752396227049,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":24499,"timestamp":514494817325,"id":807,"parentId":750,"tags":{},"startTime":1752396227348,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":686465,"timestamp":514494161417,"id":750,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396226692,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":6258,"timestamp":514494870655,"id":809,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396227401,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4615,"timestamp":514494883222,"id":810,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396227414,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":225595,"timestamp":514494862782,"id":808,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396227393,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":5,"timestamp":514495088505,"id":811,"parentId":808,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"416718848","memory.heapUsed":"204170616","memory.heapTotal":"245710848"},"startTime":1752396227619,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10901,"timestamp":514511375226,"id":822,"parentId":814,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752396243906,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":26671,"timestamp":514511359656,"id":816,"parentId":815,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396243890,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":33221,"timestamp":514511359927,"id":819,"parentId":815,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396243890,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":33922,"timestamp":514511359957,"id":820,"parentId":815,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396243890,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":37812,"timestamp":514511394953,"id":825,"parentId":824,"tags":{},"startTime":1752396243925,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":38382,"timestamp":514511394909,"id":824,"parentId":823,"tags":{},"startTime":1752396243925,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":21610,"timestamp":514511433360,"id":826,"parentId":823,"tags":{"astUsed":"true"},"startTime":1752396243964,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":100818,"timestamp":514511359863,"id":817,"parentId":815,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396243890,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":71679,"timestamp":514511389479,"id":823,"parentId":821,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396243920,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":91854,"timestamp":514511373922,"id":821,"parentId":814,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396243904,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":54,"timestamp":514511466047,"id":827,"parentId":821,"tags":{},"startTime":1752396243997,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":106231,"timestamp":514511359897,"id":818,"parentId":815,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396243890,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":108214,"timestamp":514511357938,"id":815,"parentId":814,"tags":{},"startTime":1752396243888,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2310,"timestamp":514511470356,"id":829,"parentId":828,"tags":{},"startTime":1752396244001,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":8,"timestamp":514511472738,"id":831,"parentId":828,"tags":{},"startTime":1752396244003,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":115,"timestamp":514511472926,"id":832,"parentId":828,"tags":{},"startTime":1752396244003,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":15,"timestamp":514511473110,"id":833,"parentId":828,"tags":{},"startTime":1752396244004,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":15,"timestamp":514511473174,"id":834,"parentId":828,"tags":{},"startTime":1752396244004,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2820,"timestamp":514511472720,"id":830,"parentId":828,"tags":{},"startTime":1752396244003,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":475,"timestamp":514511482560,"id":835,"parentId":828,"tags":{},"startTime":1752396244013,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3802,"timestamp":514511483089,"id":836,"parentId":828,"tags":{},"startTime":1752396244014,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":9607,"timestamp":514511490285,"id":837,"parentId":828,"tags":{},"startTime":1752396244021,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":200,"timestamp":514511499888,"id":838,"parentId":828,"tags":{},"startTime":1752396244030,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":229,"timestamp":514511500071,"id":839,"parentId":828,"tags":{},"startTime":1752396244031,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":5460,"timestamp":514511500316,"id":840,"parentId":828,"tags":{},"startTime":1752396244031,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":534,"timestamp":514511509430,"id":842,"parentId":814,"tags":{},"startTime":1752396244040,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":940,"timestamp":514511509040,"id":841,"parentId":814,"tags":{},"startTime":1752396244040,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":43615,"timestamp":514511468523,"id":828,"parentId":814,"tags":{},"startTime":1752396243999,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":155580,"timestamp":514511356654,"id":814,"parentId":812,"tags":{"name":"client"},"startTime":1752396243887,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":16586,"timestamp":514511512321,"id":843,"parentId":812,"tags":{},"startTime":1752396244043,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":198442,"timestamp":514511331507,"id":812,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396243862,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":6,"timestamp":514511535718,"id":846,"parentId":3,"tags":{},"startTime":1752396244066,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":4967,"timestamp":514511557883,"id":848,"parentId":844,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752396244088,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":28288,"timestamp":514511539743,"id":847,"parentId":845,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396244070,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8441,"timestamp":514511599788,"id":855,"parentId":844,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752396244130,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":82062,"timestamp":514511533588,"id":845,"parentId":844,"tags":{},"startTime":1752396244064,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2941,"timestamp":514511622675,"id":857,"parentId":856,"tags":{},"startTime":1752396244153,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":11,"timestamp":514511625695,"id":859,"parentId":856,"tags":{},"startTime":1752396244156,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2208,"timestamp":514511625912,"id":860,"parentId":856,"tags":{},"startTime":1752396244156,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":18,"timestamp":514511628195,"id":861,"parentId":856,"tags":{},"startTime":1752396244159,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":10,"timestamp":514511628253,"id":862,"parentId":856,"tags":{},"startTime":1752396244159,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3644,"timestamp":514511625676,"id":858,"parentId":856,"tags":{},"startTime":1752396244156,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":737,"timestamp":514511631359,"id":863,"parentId":856,"tags":{},"startTime":1752396244162,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":4653,"timestamp":514511632136,"id":864,"parentId":856,"tags":{},"startTime":1752396244163,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":1968,"timestamp":514511640521,"id":865,"parentId":856,"tags":{},"startTime":1752396244171,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":177,"timestamp":514511642483,"id":866,"parentId":856,"tags":{},"startTime":1752396244173,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":262,"timestamp":514511642642,"id":867,"parentId":856,"tags":{},"startTime":1752396244173,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":1841,"timestamp":514511642919,"id":868,"parentId":856,"tags":{},"startTime":1752396244173,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":298000,"timestamp":514511334634,"id":869,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1752396244177,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":28533,"timestamp":514511619965,"id":856,"parentId":844,"tags":{},"startTime":1752396244150,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":121137,"timestamp":514511533147,"id":844,"parentId":813,"tags":{"name":"server"},"startTime":1752396244064,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":26554,"timestamp":514511654428,"id":870,"parentId":813,"tags":{},"startTime":1752396244185,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":350775,"timestamp":514511332492,"id":813,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396243863,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4070,"timestamp":514511696224,"id":872,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396244227,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4578,"timestamp":514511704246,"id":873,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396244235,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":173046,"timestamp":514511694222,"id":871,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396244225,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":5,"timestamp":514511867355,"id":874,"parentId":871,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"432328704","memory.heapUsed":"227555328","memory.heapTotal":"255307776"},"startTime":1752396244398,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":4392,"timestamp":514516742983,"id":880,"parentId":877,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752396249274,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":38855,"timestamp":514516716423,"id":879,"parentId":878,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396249247,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":7503,"timestamp":514516769114,"id":887,"parentId":877,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752396249300,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":69286,"timestamp":514516710602,"id":878,"parentId":877,"tags":{},"startTime":1752396249241,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2748,"timestamp":514516784145,"id":889,"parentId":888,"tags":{},"startTime":1752396249315,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":9,"timestamp":514516787004,"id":891,"parentId":888,"tags":{},"startTime":1752396249318,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2695,"timestamp":514516787045,"id":892,"parentId":888,"tags":{},"startTime":1752396249318,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":11,"timestamp":514516789808,"id":893,"parentId":888,"tags":{},"startTime":1752396249320,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":9,"timestamp":514516789844,"id":894,"parentId":888,"tags":{},"startTime":1752396249320,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3850,"timestamp":514516786978,"id":890,"parentId":888,"tags":{},"startTime":1752396249318,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":545,"timestamp":514516791857,"id":895,"parentId":888,"tags":{},"startTime":1752396249322,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3212,"timestamp":514516792433,"id":896,"parentId":888,"tags":{},"startTime":1752396249323,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":10674,"timestamp":514516798911,"id":897,"parentId":888,"tags":{},"startTime":1752396249329,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":153,"timestamp":514516809580,"id":898,"parentId":888,"tags":{},"startTime":1752396249340,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":142,"timestamp":514516809720,"id":899,"parentId":888,"tags":{},"startTime":1752396249340,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":1954,"timestamp":514516809873,"id":900,"parentId":888,"tags":{},"startTime":1752396249340,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":32502,"timestamp":514516782275,"id":888,"parentId":877,"tags":{},"startTime":1752396249313,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":110406,"timestamp":514516709781,"id":877,"parentId":875,"tags":{"name":"server"},"startTime":1752396249240,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":22574,"timestamp":514516820598,"id":901,"parentId":875,"tags":{},"startTime":1752396249351,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":157657,"timestamp":514516686312,"id":875,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396249217,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":10450,"timestamp":514516849819,"id":904,"parentId":903,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396249380,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10182,"timestamp":514516856513,"id":910,"parentId":902,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752396249387,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":24316,"timestamp":514516849950,"id":907,"parentId":903,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396249380,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":25784,"timestamp":514516849987,"id":908,"parentId":903,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396249381,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":43647,"timestamp":514516877684,"id":913,"parentId":912,"tags":{},"startTime":1752396249408,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":44142,"timestamp":514516877638,"id":912,"parentId":911,"tags":{},"startTime":1752396249408,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":18101,"timestamp":514516921863,"id":914,"parentId":911,"tags":{"astUsed":"true"},"startTime":1752396249452,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":94117,"timestamp":514516849929,"id":905,"parentId":903,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396249380,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":75716,"timestamp":514516868880,"id":911,"parentId":909,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396249399,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":103503,"timestamp":514516855848,"id":909,"parentId":902,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396249386,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":134,"timestamp":514516960392,"id":915,"parentId":909,"tags":{},"startTime":1752396249491,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":111023,"timestamp":514516849942,"id":906,"parentId":903,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396249380,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":114853,"timestamp":514516846189,"id":903,"parentId":902,"tags":{},"startTime":1752396249377,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3430,"timestamp":514516971449,"id":917,"parentId":916,"tags":{},"startTime":1752396249502,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":12,"timestamp":514516975103,"id":919,"parentId":916,"tags":{},"startTime":1752396249506,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":109,"timestamp":514516975148,"id":920,"parentId":916,"tags":{},"startTime":1752396249506,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":12,"timestamp":514516975300,"id":921,"parentId":916,"tags":{},"startTime":1752396249506,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":33,"timestamp":514516975349,"id":922,"parentId":916,"tags":{},"startTime":1752396249506,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":1897,"timestamp":514516974935,"id":918,"parentId":916,"tags":{},"startTime":1752396249505,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":977,"timestamp":514516979051,"id":923,"parentId":916,"tags":{},"startTime":1752396249510,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":4684,"timestamp":514516980076,"id":924,"parentId":916,"tags":{},"startTime":1752396249511,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":8521,"timestamp":514516989004,"id":925,"parentId":916,"tags":{},"startTime":1752396249520,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":485,"timestamp":514516997517,"id":926,"parentId":916,"tags":{},"startTime":1752396249528,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":850,"timestamp":514516997961,"id":927,"parentId":916,"tags":{},"startTime":1752396249528,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":28926,"timestamp":514516998877,"id":928,"parentId":916,"tags":{},"startTime":1752396249529,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":266,"timestamp":514517032678,"id":930,"parentId":902,"tags":{},"startTime":1752396249563,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":642,"timestamp":514517032362,"id":929,"parentId":902,"tags":{},"startTime":1752396249563,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":74027,"timestamp":514516966797,"id":916,"parentId":902,"tags":{},"startTime":1752396249497,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":195204,"timestamp":514516845801,"id":902,"parentId":876,"tags":{"name":"client"},"startTime":1752396249376,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":53606,"timestamp":514517041113,"id":931,"parentId":876,"tags":{},"startTime":1752396249572,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":412635,"timestamp":514516686661,"id":876,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396249217,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4150,"timestamp":514517111113,"id":933,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396249642,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":3359,"timestamp":514517119107,"id":934,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396249650,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":5,"timestamp":514517190061,"id":935,"parentId":3,"tags":{},"startTime":1752396249721,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":86845,"timestamp":514517109453,"id":932,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396249640,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":4,"timestamp":514517196414,"id":936,"parentId":932,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"437063680","memory.heapUsed":"224743608","memory.heapTotal":"270020608"},"startTime":1752396249727,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":556000,"timestamp":514516689679,"id":937,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1752396249775,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":21072,"timestamp":514543958001,"id":943,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"rsc"},"startTime":1752396276489,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":57009,"timestamp":514543930529,"id":942,"parentId":941,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396276461,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":12800,"timestamp":514544020049,"id":950,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"ssr"},"startTime":1752396276551,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":116791,"timestamp":514543924386,"id":941,"parentId":940,"tags":{},"startTime":1752396276455,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3672,"timestamp":514544045170,"id":952,"parentId":951,"tags":{},"startTime":1752396276574,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":10,"timestamp":514544048903,"id":954,"parentId":951,"tags":{},"startTime":1752396276578,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":1382,"timestamp":514544048938,"id":955,"parentId":951,"tags":{},"startTime":1752396276578,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":16,"timestamp":514544050364,"id":956,"parentId":951,"tags":{},"startTime":1752396276579,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":7,"timestamp":514544050402,"id":957,"parentId":951,"tags":{},"startTime":1752396276579,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2909,"timestamp":514544048887,"id":953,"parentId":951,"tags":{},"startTime":1752396276578,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":807,"timestamp":514544054497,"id":958,"parentId":951,"tags":{},"startTime":1752396276583,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5875,"timestamp":514544055360,"id":959,"parentId":951,"tags":{},"startTime":1752396276584,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3669,"timestamp":514544063971,"id":960,"parentId":951,"tags":{},"startTime":1752396276593,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":291,"timestamp":514544067634,"id":961,"parentId":951,"tags":{},"startTime":1752396276596,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":247,"timestamp":514544067894,"id":962,"parentId":951,"tags":{},"startTime":1752396276597,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":3884,"timestamp":514544068167,"id":963,"parentId":951,"tags":{},"startTime":1752396276597,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":33208,"timestamp":514544043723,"id":951,"parentId":940,"tags":{},"startTime":1752396276572,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":160553,"timestamp":514543922188,"id":940,"parentId":938,"tags":{"name":"server"},"startTime":1752396276453,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":15956,"timestamp":514544082864,"id":964,"parentId":938,"tags":{},"startTime":1752396276612,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":190810,"timestamp":514543909073,"id":938,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396276440,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":12649,"timestamp":514544126363,"id":973,"parentId":965,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Hero.tsx","layer":"app-pages-browser"},"startTime":1752396276655,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":22869,"timestamp":514544116388,"id":967,"parentId":966,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396276645,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":29804,"timestamp":514544116554,"id":970,"parentId":966,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396276645,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":30770,"timestamp":514544116572,"id":971,"parentId":966,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396276645,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":33695,"timestamp":514544147424,"id":976,"parentId":975,"tags":{},"startTime":1752396276676,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":34033,"timestamp":514544147399,"id":975,"parentId":974,"tags":{},"startTime":1752396276676,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":24649,"timestamp":514544181493,"id":977,"parentId":974,"tags":{"astUsed":"true"},"startTime":1752396276710,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":101580,"timestamp":514544116512,"id":968,"parentId":966,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396276645,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":73760,"timestamp":514544144961,"id":974,"parentId":972,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396276674,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":106090,"timestamp":514544125326,"id":972,"parentId":965,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396276654,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":96,"timestamp":514544231798,"id":978,"parentId":972,"tags":{},"startTime":1752396276760,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":115410,"timestamp":514544116537,"id":969,"parentId":966,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396276645,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":128007,"timestamp":514544103989,"id":966,"parentId":965,"tags":{},"startTime":1752396276633,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2018,"timestamp":514544235552,"id":980,"parentId":979,"tags":{},"startTime":1752396276764,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":14,"timestamp":514544237649,"id":982,"parentId":979,"tags":{},"startTime":1752396276766,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":108,"timestamp":514544237700,"id":983,"parentId":979,"tags":{},"startTime":1752396276766,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":15,"timestamp":514544237863,"id":984,"parentId":979,"tags":{},"startTime":1752396276767,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":21,"timestamp":514544237924,"id":985,"parentId":979,"tags":{},"startTime":1752396276767,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3462,"timestamp":514544237631,"id":981,"parentId":979,"tags":{},"startTime":1752396276766,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":522,"timestamp":514544243840,"id":986,"parentId":979,"tags":{},"startTime":1752396276773,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":4269,"timestamp":514544244394,"id":987,"parentId":979,"tags":{},"startTime":1752396276773,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":7164,"timestamp":514544250888,"id":988,"parentId":979,"tags":{},"startTime":1752396276780,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":306,"timestamp":514544258046,"id":989,"parentId":979,"tags":{},"startTime":1752396276787,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":272,"timestamp":514544258332,"id":990,"parentId":979,"tags":{},"startTime":1752396276787,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":7533,"timestamp":514544258923,"id":991,"parentId":979,"tags":{},"startTime":1752396276788,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":168,"timestamp":514544268419,"id":993,"parentId":965,"tags":{},"startTime":1752396276797,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":332,"timestamp":514544268276,"id":992,"parentId":965,"tags":{},"startTime":1752396276797,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":37582,"timestamp":514544234113,"id":979,"parentId":965,"tags":{},"startTime":1752396276763,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":168531,"timestamp":514544103292,"id":965,"parentId":939,"tags":{"name":"client"},"startTime":1752396276632,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":49024,"timestamp":514544271911,"id":994,"parentId":939,"tags":{},"startTime":1752396276801,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":420327,"timestamp":514543909316,"id":939,"parentId":3,"tags":{"trigger":"src/components/Hero.tsx"},"startTime":1752396276440,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":5202,"timestamp":514544348353,"id":996,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396276877,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":11,"timestamp":514544365452,"id":998,"parentId":3,"tags":{},"startTime":1752396276894,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":5771,"timestamp":514544361173,"id":997,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396276890,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":219380,"timestamp":514544345013,"id":995,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396276874,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":8,"timestamp":514544564562,"id":999,"parentId":995,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"398254080","memory.heapUsed":"205987680","memory.heapTotal":"249278464"},"startTime":1752396277093,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":737000,"timestamp":514543913885,"id":1000,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":false},"startTime":1752396277178,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":19710,"timestamp":514569063949,"id":1006,"parentId":1003,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752396301593,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":71696,"timestamp":514569031031,"id":1005,"parentId":1004,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396301560,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":25569,"timestamp":514569184382,"id":1013,"parentId":1003,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752396301713,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":203138,"timestamp":514569024949,"id":1004,"parentId":1003,"tags":{},"startTime":1752396301554,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":9695,"timestamp":514569243252,"id":1015,"parentId":1014,"tags":{},"startTime":1752396301772,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":52,"timestamp":514569253166,"id":1017,"parentId":1014,"tags":{},"startTime":1752396301782,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":6200,"timestamp":514569253319,"id":1018,"parentId":1014,"tags":{},"startTime":1752396301782,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":74,"timestamp":514569259683,"id":1019,"parentId":1014,"tags":{},"startTime":1752396301788,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":33,"timestamp":514569259848,"id":1020,"parentId":1014,"tags":{},"startTime":1752396301789,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":10635,"timestamp":514569253110,"id":1016,"parentId":1014,"tags":{},"startTime":1752396301782,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1984,"timestamp":514569272269,"id":1021,"parentId":1014,"tags":{},"startTime":1752396301801,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":25885,"timestamp":514569274412,"id":1022,"parentId":1014,"tags":{},"startTime":1752396301803,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":4748,"timestamp":514569306859,"id":1023,"parentId":1014,"tags":{},"startTime":1752396301836,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":350,"timestamp":514569311599,"id":1024,"parentId":1014,"tags":{},"startTime":1752396301840,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":280,"timestamp":514569311913,"id":1025,"parentId":1014,"tags":{},"startTime":1752396301841,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":10223,"timestamp":514569312216,"id":1026,"parentId":1014,"tags":{},"startTime":1752396301841,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":95006,"timestamp":514569233670,"id":1014,"parentId":1003,"tags":{},"startTime":1752396301762,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":320007,"timestamp":514569023791,"id":1003,"parentId":1001,"tags":{"name":"server"},"startTime":1752396301552,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":27537,"timestamp":514569343967,"id":1027,"parentId":1001,"tags":{},"startTime":1752396301873,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":365283,"timestamp":514569007419,"id":1001,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396301536,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":30860,"timestamp":514569397042,"id":1036,"parentId":1028,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396301926,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":41067,"timestamp":514569387362,"id":1030,"parentId":1029,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396301916,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":54191,"timestamp":514569387596,"id":1033,"parentId":1029,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396301916,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":55540,"timestamp":514569387617,"id":1034,"parentId":1029,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396301916,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":81849,"timestamp":514569443348,"id":1039,"parentId":1038,"tags":{},"startTime":1752396301972,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":83227,"timestamp":514569443299,"id":1038,"parentId":1037,"tags":{},"startTime":1752396301972,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":51944,"timestamp":514569526704,"id":1040,"parentId":1037,"tags":{"astUsed":"true"},"startTime":1752396302055,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":197967,"timestamp":514569387538,"id":1031,"parentId":1029,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396301916,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":146700,"timestamp":514569439242,"id":1037,"parentId":1035,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396301968,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":198330,"timestamp":514569395322,"id":1035,"parentId":1028,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396301924,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":81,"timestamp":514569593959,"id":1041,"parentId":1035,"tags":{},"startTime":1752396302123,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":206514,"timestamp":514569387572,"id":1032,"parentId":1029,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396301916,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":217185,"timestamp":514569376944,"id":1029,"parentId":1028,"tags":{},"startTime":1752396301906,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":7401,"timestamp":514569600142,"id":1043,"parentId":1042,"tags":{},"startTime":1752396302129,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":11,"timestamp":514569607638,"id":1045,"parentId":1042,"tags":{},"startTime":1752396302136,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":109,"timestamp":514569607686,"id":1046,"parentId":1042,"tags":{},"startTime":1752396302136,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":10,"timestamp":514569607838,"id":1047,"parentId":1042,"tags":{},"startTime":1752396302137,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":8,"timestamp":514569607882,"id":1048,"parentId":1042,"tags":{},"startTime":1752396302137,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2464,"timestamp":514569607613,"id":1044,"parentId":1042,"tags":{},"startTime":1752396302136,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":601,"timestamp":514569611662,"id":1049,"parentId":1042,"tags":{},"startTime":1752396302140,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5347,"timestamp":514569612313,"id":1050,"parentId":1042,"tags":{},"startTime":1752396302141,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":8285,"timestamp":514569620619,"id":1051,"parentId":1042,"tags":{},"startTime":1752396302149,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":322,"timestamp":514569628895,"id":1052,"parentId":1042,"tags":{},"startTime":1752396302158,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":305,"timestamp":514569629190,"id":1053,"parentId":1042,"tags":{},"startTime":1752396302158,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":8826,"timestamp":514569629524,"id":1054,"parentId":1042,"tags":{},"startTime":1752396302158,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":194,"timestamp":514569640700,"id":1056,"parentId":1028,"tags":{},"startTime":1752396302169,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":372,"timestamp":514569640545,"id":1055,"parentId":1028,"tags":{},"startTime":1752396302169,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":47148,"timestamp":514569596759,"id":1042,"parentId":1028,"tags":{},"startTime":1752396302125,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":267778,"timestamp":514569376259,"id":1028,"parentId":1002,"tags":{"name":"client"},"startTime":1752396301905,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":41868,"timestamp":514569644136,"id":1057,"parentId":1002,"tags":{},"startTime":1752396302173,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":683082,"timestamp":514569007688,"id":1002,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396301536,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":3734,"timestamp":514569702308,"id":1059,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396302231,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":5168,"timestamp":514569713168,"id":1060,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396302242,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":10,"timestamp":514569839034,"id":1061,"parentId":3,"tags":{},"startTime":1752396302368,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":150079,"timestamp":514569699165,"id":1058,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396302228,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":7,"timestamp":514569849371,"id":1062,"parentId":1058,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"405942272","memory.heapUsed":"223245576","memory.heapTotal":"246910976"},"startTime":1752396302378,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":914000,"timestamp":514569012768,"id":1063,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752396302452,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":12357,"timestamp":514579601727,"id":1069,"parentId":1066,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752396312130,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":63056,"timestamp":514579578940,"id":1068,"parentId":1067,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396312108,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":22956,"timestamp":514579684418,"id":1076,"parentId":1066,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752396312213,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":145208,"timestamp":514579572965,"id":1067,"parentId":1066,"tags":{},"startTime":1752396312102,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":4798,"timestamp":514579727283,"id":1078,"parentId":1077,"tags":{},"startTime":1752396312256,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":30,"timestamp":514579732187,"id":1080,"parentId":1077,"tags":{},"startTime":1752396312261,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":5771,"timestamp":514579732271,"id":1081,"parentId":1077,"tags":{},"startTime":1752396312261,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":104,"timestamp":514579738195,"id":1082,"parentId":1077,"tags":{},"startTime":1752396312267,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":33,"timestamp":514579738417,"id":1083,"parentId":1077,"tags":{},"startTime":1752396312267,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":9600,"timestamp":514579732156,"id":1079,"parentId":1077,"tags":{},"startTime":1752396312261,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1413,"timestamp":514579744311,"id":1084,"parentId":1077,"tags":{},"startTime":1752396312273,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":1754,"timestamp":514579745782,"id":1085,"parentId":1077,"tags":{},"startTime":1752396312274,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":1030,"timestamp":514579748392,"id":1086,"parentId":1077,"tags":{},"startTime":1752396312277,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":89,"timestamp":514579749421,"id":1087,"parentId":1077,"tags":{},"startTime":1752396312278,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":179,"timestamp":514579749501,"id":1088,"parentId":1077,"tags":{},"startTime":1752396312278,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":2194,"timestamp":514579749693,"id":1089,"parentId":1077,"tags":{},"startTime":1752396312278,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":32071,"timestamp":514579724705,"id":1077,"parentId":1066,"tags":{},"startTime":1752396312253,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":193710,"timestamp":514579572077,"id":1066,"parentId":1064,"tags":{"name":"server"},"startTime":1752396312101,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":21744,"timestamp":514579765917,"id":1090,"parentId":1064,"tags":{},"startTime":1752396312295,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":227416,"timestamp":514579561544,"id":1064,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396312090,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":10559,"timestamp":514579796076,"id":1093,"parentId":1092,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396312325,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10878,"timestamp":514579801226,"id":1099,"parentId":1091,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396312330,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":19623,"timestamp":514579796164,"id":1096,"parentId":1092,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396312325,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":20230,"timestamp":514579796170,"id":1097,"parentId":1092,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396312325,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":56986,"timestamp":514579817378,"id":1102,"parentId":1101,"tags":{},"startTime":1752396312346,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":57494,"timestamp":514579817315,"id":1101,"parentId":1100,"tags":{},"startTime":1752396312346,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":19691,"timestamp":514579874885,"id":1103,"parentId":1100,"tags":{"astUsed":"true"},"startTime":1752396312404,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":101732,"timestamp":514579796147,"id":1094,"parentId":1092,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396312325,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":85705,"timestamp":514579813488,"id":1100,"parentId":1098,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396312342,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":107545,"timestamp":514579800233,"id":1098,"parentId":1091,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396312329,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":149,"timestamp":514579908231,"id":1104,"parentId":1098,"tags":{},"startTime":1752396312437,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":112304,"timestamp":514579796157,"id":1095,"parentId":1092,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396312325,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":116409,"timestamp":514579792107,"id":1092,"parentId":1091,"tags":{},"startTime":1752396312321,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":8954,"timestamp":514579912688,"id":1106,"parentId":1105,"tags":{},"startTime":1752396312441,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":8,"timestamp":514579921732,"id":1108,"parentId":1105,"tags":{},"startTime":1752396312450,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":105,"timestamp":514579921767,"id":1109,"parentId":1105,"tags":{},"startTime":1752396312450,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":9,"timestamp":514579921910,"id":1110,"parentId":1105,"tags":{},"startTime":1752396312451,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":7,"timestamp":514579921950,"id":1111,"parentId":1105,"tags":{},"startTime":1752396312451,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2141,"timestamp":514579921711,"id":1107,"parentId":1105,"tags":{},"startTime":1752396312450,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":752,"timestamp":514579926985,"id":1112,"parentId":1105,"tags":{},"startTime":1752396312456,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3131,"timestamp":514579927802,"id":1113,"parentId":1105,"tags":{},"startTime":1752396312456,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":9492,"timestamp":514579932911,"id":1114,"parentId":1105,"tags":{},"startTime":1752396312462,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":1140,"timestamp":514579942388,"id":1115,"parentId":1105,"tags":{},"startTime":1752396312471,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":841,"timestamp":514579943076,"id":1116,"parentId":1105,"tags":{},"startTime":1752396312472,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":14719,"timestamp":514579943971,"id":1117,"parentId":1105,"tags":{},"startTime":1752396312473,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":256,"timestamp":514579963998,"id":1119,"parentId":1091,"tags":{},"startTime":1752396312493,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":462,"timestamp":514579963818,"id":1118,"parentId":1091,"tags":{},"startTime":1752396312492,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":56573,"timestamp":514579910464,"id":1105,"parentId":1091,"tags":{},"startTime":1752396312439,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":175481,"timestamp":514579791629,"id":1091,"parentId":1065,"tags":{"name":"client"},"startTime":1752396312320,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":35959,"timestamp":514579967159,"id":1120,"parentId":1065,"tags":{},"startTime":1752396312496,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":445290,"timestamp":514579561766,"id":1065,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396312090,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":3626,"timestamp":514580018198,"id":1122,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396312547,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":2818,"timestamp":514580023910,"id":1123,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396312553,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":5,"timestamp":514580105223,"id":1124,"parentId":3,"tags":{},"startTime":1752396312634,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":95384,"timestamp":514580016667,"id":1121,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396312545,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":4,"timestamp":514580112180,"id":1125,"parentId":1121,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"427511808","memory.heapUsed":"222367184","memory.heapTotal":"259366912"},"startTime":1752396312641,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":647000,"timestamp":514579566783,"id":1126,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752396312739,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":18727,"timestamp":514635242426,"id":1132,"parentId":1129,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752396367775,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":65825,"timestamp":514635211887,"id":1131,"parentId":1130,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396367744,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":29276,"timestamp":514635316236,"id":1139,"parentId":1129,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752396367848,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":149594,"timestamp":514635204819,"id":1130,"parentId":1129,"tags":{},"startTime":1752396367737,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":4327,"timestamp":514635359000,"id":1141,"parentId":1140,"tags":{},"startTime":1752396367891,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":15,"timestamp":514635363408,"id":1143,"parentId":1140,"tags":{},"startTime":1752396367896,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2352,"timestamp":514635363453,"id":1144,"parentId":1140,"tags":{},"startTime":1752396367896,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":39,"timestamp":514635365889,"id":1145,"parentId":1140,"tags":{},"startTime":1752396367898,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":12,"timestamp":514635365971,"id":1146,"parentId":1140,"tags":{},"startTime":1752396367898,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5083,"timestamp":514635363385,"id":1142,"parentId":1140,"tags":{},"startTime":1752396367896,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":797,"timestamp":514635372633,"id":1147,"parentId":1140,"tags":{},"startTime":1752396367905,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":6267,"timestamp":514635373479,"id":1148,"parentId":1140,"tags":{},"startTime":1752396367906,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3323,"timestamp":514635381936,"id":1149,"parentId":1140,"tags":{},"startTime":1752396367914,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":204,"timestamp":514635385254,"id":1150,"parentId":1140,"tags":{},"startTime":1752396367917,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":114,"timestamp":514635385441,"id":1151,"parentId":1140,"tags":{},"startTime":1752396367918,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":3375,"timestamp":514635385571,"id":1152,"parentId":1140,"tags":{},"startTime":1752396367918,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":36212,"timestamp":514635356831,"id":1140,"parentId":1129,"tags":{},"startTime":1752396367889,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":198522,"timestamp":514635202929,"id":1129,"parentId":1127,"tags":{"name":"server"},"startTime":1752396367735,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":19952,"timestamp":514635401594,"id":1153,"parentId":1127,"tags":{},"startTime":1752396367934,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":234767,"timestamp":514635188029,"id":1127,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396367720,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":9227,"timestamp":514635437556,"id":1156,"parentId":1155,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396367970,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":17554,"timestamp":514635442761,"id":1162,"parentId":1154,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396367975,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":28260,"timestamp":514635437669,"id":1159,"parentId":1155,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396367970,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":35261,"timestamp":514635437675,"id":1160,"parentId":1155,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396367970,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":72566,"timestamp":514635473284,"id":1165,"parentId":1164,"tags":{},"startTime":1752396368005,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":73747,"timestamp":514635473189,"id":1164,"parentId":1163,"tags":{},"startTime":1752396368005,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":54554,"timestamp":514635547104,"id":1166,"parentId":1163,"tags":{"astUsed":"true"},"startTime":1752396368079,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":173923,"timestamp":514635437648,"id":1157,"parentId":1155,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396367970,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":150312,"timestamp":514635461889,"id":1163,"parentId":1161,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396367994,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":195744,"timestamp":514635442149,"id":1161,"parentId":1154,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396367974,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":568,"timestamp":514635639792,"id":1167,"parentId":1161,"tags":{},"startTime":1752396368172,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":203164,"timestamp":514635437661,"id":1158,"parentId":1155,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396367970,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":213539,"timestamp":514635427820,"id":1155,"parentId":1154,"tags":{},"startTime":1752396367960,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":9319,"timestamp":514635663651,"id":1169,"parentId":1168,"tags":{},"startTime":1752396368196,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":11,"timestamp":514635673092,"id":1171,"parentId":1168,"tags":{},"startTime":1752396368205,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":139,"timestamp":514635673147,"id":1172,"parentId":1168,"tags":{},"startTime":1752396368205,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":13,"timestamp":514635673360,"id":1173,"parentId":1168,"tags":{},"startTime":1752396368206,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":10,"timestamp":514635673420,"id":1174,"parentId":1168,"tags":{},"startTime":1752396368206,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3037,"timestamp":514635673064,"id":1170,"parentId":1168,"tags":{},"startTime":1752396368205,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1355,"timestamp":514635681173,"id":1175,"parentId":1168,"tags":{},"startTime":1752396368213,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5558,"timestamp":514635682628,"id":1176,"parentId":1168,"tags":{},"startTime":1752396368215,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":14348,"timestamp":514635696499,"id":1177,"parentId":1168,"tags":{},"startTime":1752396368229,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":1268,"timestamp":514635710838,"id":1178,"parentId":1168,"tags":{},"startTime":1752396368243,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":973,"timestamp":514635711502,"id":1179,"parentId":1168,"tags":{},"startTime":1752396368244,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":10491,"timestamp":514635712546,"id":1180,"parentId":1168,"tags":{},"startTime":1752396368245,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":210,"timestamp":514635726655,"id":1182,"parentId":1154,"tags":{},"startTime":1752396368259,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":483,"timestamp":514635726418,"id":1181,"parentId":1154,"tags":{},"startTime":1752396368259,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":75243,"timestamp":514635657336,"id":1168,"parentId":1154,"tags":{},"startTime":1752396368190,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":306272,"timestamp":514635426640,"id":1154,"parentId":1128,"tags":{"name":"client"},"startTime":1752396367959,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":74457,"timestamp":514635733169,"id":1183,"parentId":1128,"tags":{},"startTime":1752396368265,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":638789,"timestamp":514635188233,"id":1128,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396367720,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":5858,"timestamp":514635958139,"id":1185,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396368490,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":3858,"timestamp":514635969113,"id":1186,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396368501,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":7,"timestamp":514636107334,"id":1187,"parentId":3,"tags":{},"startTime":1752396368639,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":167761,"timestamp":514635953704,"id":1184,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396368486,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":11,"timestamp":514636121701,"id":1188,"parentId":1184,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"409116672","memory.heapUsed":"215403048","memory.heapTotal":"238563328"},"startTime":1752396368654,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":987000,"timestamp":514635190135,"id":1189,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752396368710,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":32702,"timestamp":514722357589,"id":1200,"parentId":1192,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396454889,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":49459,"timestamp":514722341068,"id":1194,"parentId":1193,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396454872,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":61167,"timestamp":514722341232,"id":1197,"parentId":1193,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396454873,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":63412,"timestamp":514722341241,"id":1198,"parentId":1193,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396454873,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":95580,"timestamp":514722405807,"id":1203,"parentId":1202,"tags":{},"startTime":1752396454937,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":96046,"timestamp":514722405752,"id":1202,"parentId":1201,"tags":{},"startTime":1752396454937,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":41466,"timestamp":514722501889,"id":1204,"parentId":1201,"tags":{"astUsed":"true"},"startTime":1752396455033,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":210370,"timestamp":514722341205,"id":1195,"parentId":1193,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396454872,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":155274,"timestamp":514722396841,"id":1201,"parentId":1199,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396454928,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":206648,"timestamp":514722356053,"id":1199,"parentId":1192,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396454887,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":158,"timestamp":514722563119,"id":1205,"parentId":1199,"tags":{},"startTime":1752396455094,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":222143,"timestamp":514722341222,"id":1196,"parentId":1193,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396454873,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":223107,"timestamp":514722340324,"id":1193,"parentId":1192,"tags":{},"startTime":1752396454872,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":9249,"timestamp":514722636100,"id":1207,"parentId":1206,"tags":{},"startTime":1752396455167,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":51,"timestamp":514722645535,"id":1209,"parentId":1206,"tags":{},"startTime":1752396455177,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":278,"timestamp":514722645917,"id":1210,"parentId":1206,"tags":{},"startTime":1752396455177,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":84,"timestamp":514722646320,"id":1211,"parentId":1206,"tags":{},"startTime":1752396455178,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":40,"timestamp":514722646528,"id":1212,"parentId":1206,"tags":{},"startTime":1752396455178,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":6262,"timestamp":514722645480,"id":1208,"parentId":1206,"tags":{},"startTime":1752396455177,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2747,"timestamp":514722657846,"id":1213,"parentId":1206,"tags":{},"startTime":1752396455189,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":9006,"timestamp":514722660691,"id":1214,"parentId":1206,"tags":{},"startTime":1752396455192,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":8286,"timestamp":514722694712,"id":1215,"parentId":1206,"tags":{},"startTime":1752396455226,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":299,"timestamp":514722702991,"id":1216,"parentId":1206,"tags":{},"startTime":1752396455234,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":208,"timestamp":514722703267,"id":1217,"parentId":1206,"tags":{},"startTime":1752396455235,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":9629,"timestamp":514722703491,"id":1218,"parentId":1206,"tags":{},"startTime":1752396455235,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":193,"timestamp":514722715721,"id":1220,"parentId":1192,"tags":{},"startTime":1752396455247,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":400,"timestamp":514722715540,"id":1219,"parentId":1192,"tags":{},"startTime":1752396455247,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":88462,"timestamp":514722630384,"id":1206,"parentId":1192,"tags":{},"startTime":1752396455162,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":379266,"timestamp":514722339666,"id":1192,"parentId":1190,"tags":{"name":"client"},"startTime":1752396454871,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":35055,"timestamp":514722719126,"id":1221,"parentId":1190,"tags":{},"startTime":1752396455250,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":430468,"timestamp":514722326111,"id":1190,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396454857,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":8,"timestamp":514722773468,"id":1225,"parentId":3,"tags":{},"startTime":1752396455305,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":6626,"timestamp":514722796909,"id":1227,"parentId":1222,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752396455328,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":33953,"timestamp":514722778928,"id":1226,"parentId":1223,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396455310,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":13839,"timestamp":514722846570,"id":1234,"parentId":1222,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752396455378,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":104000,"timestamp":514722763466,"id":1223,"parentId":1222,"tags":{},"startTime":1752396455295,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":8958,"timestamp":514722946120,"id":1236,"parentId":1235,"tags":{},"startTime":1752396455477,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":18,"timestamp":514722955213,"id":1238,"parentId":1235,"tags":{},"startTime":1752396455487,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":6861,"timestamp":514722955298,"id":1239,"parentId":1235,"tags":{},"startTime":1752396455487,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":30,"timestamp":514722962284,"id":1240,"parentId":1235,"tags":{},"startTime":1752396455494,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":18,"timestamp":514722962368,"id":1241,"parentId":1235,"tags":{},"startTime":1752396455494,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":56986,"timestamp":514722955174,"id":1237,"parentId":1235,"tags":{},"startTime":1752396455486,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":19526,"timestamp":514723030575,"id":1242,"parentId":1235,"tags":{},"startTime":1752396455562,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":79892,"timestamp":514723050283,"id":1243,"parentId":1235,"tags":{},"startTime":1752396455582,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":4644,"timestamp":514723137518,"id":1244,"parentId":1235,"tags":{},"startTime":1752396455669,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":632,"timestamp":514723142150,"id":1245,"parentId":1235,"tags":{},"startTime":1752396455673,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":374,"timestamp":514723142651,"id":1246,"parentId":1235,"tags":{},"startTime":1752396455674,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":9800,"timestamp":514723143092,"id":1247,"parentId":1235,"tags":{},"startTime":1752396455674,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":113000,"timestamp":514722781871,"id":1248,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752396455687,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":219492,"timestamp":514722940982,"id":1235,"parentId":1222,"tags":{},"startTime":1752396455472,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":411047,"timestamp":514722761662,"id":1222,"parentId":1191,"tags":{"name":"server"},"startTime":1752396455293,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":27995,"timestamp":514723172928,"id":1249,"parentId":1191,"tags":{},"startTime":1752396455704,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":875629,"timestamp":514722326767,"id":1191,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396454858,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":18563,"timestamp":514723223260,"id":1258,"parentId":1250,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396455755,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":31552,"timestamp":514723210778,"id":1252,"parentId":1251,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396455742,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":73346,"timestamp":514723244773,"id":1261,"parentId":1260,"tags":{},"startTime":1752396455776,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":75083,"timestamp":514723244721,"id":1260,"parentId":1259,"tags":{},"startTime":1752396455776,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":72174,"timestamp":514723320086,"id":1262,"parentId":1259,"tags":{"astUsed":"true"},"startTime":1752396455851,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":160469,"timestamp":514723244275,"id":1259,"parentId":1257,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396455776,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":206051,"timestamp":514723210995,"id":1255,"parentId":1251,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396455742,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":209838,"timestamp":514723222171,"id":1257,"parentId":1250,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396455753,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":222865,"timestamp":514723211006,"id":1256,"parentId":1251,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396455742,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":251,"timestamp":514723436495,"id":1263,"parentId":1257,"tags":{},"startTime":1752396455968,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":227519,"timestamp":514723210984,"id":1254,"parentId":1251,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396455742,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":228295,"timestamp":514723210965,"id":1253,"parentId":1251,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396455742,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":233584,"timestamp":514723205797,"id":1251,"parentId":1250,"tags":{},"startTime":1752396455737,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":7179,"timestamp":514723448178,"id":1265,"parentId":1264,"tags":{},"startTime":1752396455979,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":54,"timestamp":514723455573,"id":1267,"parentId":1264,"tags":{},"startTime":1752396455987,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":294,"timestamp":514723455732,"id":1268,"parentId":1264,"tags":{},"startTime":1752396455987,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":69,"timestamp":514723456162,"id":1269,"parentId":1264,"tags":{},"startTime":1752396455987,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":41,"timestamp":514723456353,"id":1270,"parentId":1264,"tags":{},"startTime":1752396455988,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":4670,"timestamp":514723455513,"id":1266,"parentId":1264,"tags":{},"startTime":1752396455987,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":3977,"timestamp":514723465698,"id":1271,"parentId":1264,"tags":{},"startTime":1752396455997,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5263,"timestamp":514723470138,"id":1272,"parentId":1264,"tags":{},"startTime":1752396456001,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":15552,"timestamp":514723481549,"id":1273,"parentId":1264,"tags":{},"startTime":1752396456013,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":702,"timestamp":514723497090,"id":1274,"parentId":1264,"tags":{},"startTime":1752396456028,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":746,"timestamp":514723497733,"id":1275,"parentId":1264,"tags":{},"startTime":1752396456029,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":16876,"timestamp":514723498599,"id":1276,"parentId":1264,"tags":{},"startTime":1752396456030,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":703,"timestamp":514723520852,"id":1278,"parentId":1250,"tags":{},"startTime":1752396456052,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":1448,"timestamp":514723520170,"id":1277,"parentId":1250,"tags":{},"startTime":1752396456051,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":83740,"timestamp":514723443278,"id":1264,"parentId":1250,"tags":{},"startTime":1752396455975,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":321889,"timestamp":514723205210,"id":1250,"parentId":1224,"tags":{"name":"client"},"startTime":1752396455737,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":32687,"timestamp":514723527145,"id":1279,"parentId":1224,"tags":{},"startTime":1752396456058,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":792675,"timestamp":514722771789,"id":1224,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396455303,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":2637,"timestamp":514723574871,"id":1281,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396456106,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":3756,"timestamp":514723580441,"id":1282,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396456112,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":10,"timestamp":514723725348,"id":1283,"parentId":3,"tags":{},"startTime":1752396456257,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":166501,"timestamp":514723572774,"id":1280,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396456104,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":7,"timestamp":514723739456,"id":1284,"parentId":1280,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"437596160","memory.heapUsed":"222308688","memory.heapTotal":"259874816"},"startTime":1752396456271,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":1029000,"timestamp":514722781339,"id":1285,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1752396456342,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":41658,"timestamp":514925691635,"id":1296,"parentId":1288,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"app-pages-browser"},"startTime":1752396658236,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":59101,"timestamp":514925675423,"id":1290,"parentId":1289,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396658220,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":70264,"timestamp":514925675678,"id":1293,"parentId":1289,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396658220,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":71341,"timestamp":514925675690,"id":1294,"parentId":1289,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396658220,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":55163,"timestamp":514925749909,"id":1299,"parentId":1298,"tags":{},"startTime":1752396658294,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":56135,"timestamp":514925749818,"id":1298,"parentId":1297,"tags":{},"startTime":1752396658294,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":25119,"timestamp":514925806114,"id":1300,"parentId":1297,"tags":{"astUsed":"true"},"startTime":1752396658350,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":160430,"timestamp":514925675656,"id":1291,"parentId":1289,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396658220,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":98034,"timestamp":514925738855,"id":1297,"parentId":1295,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396658283,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":152987,"timestamp":514925690789,"id":1295,"parentId":1288,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396658235,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":43,"timestamp":514925845797,"id":1301,"parentId":1295,"tags":{},"startTime":1752396658390,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":170202,"timestamp":514925675671,"id":1292,"parentId":1289,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396658220,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":172741,"timestamp":514925673163,"id":1289,"parentId":1288,"tags":{},"startTime":1752396658217,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":6607,"timestamp":514925913855,"id":1303,"parentId":1302,"tags":{},"startTime":1752396658458,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":37,"timestamp":514925920650,"id":1305,"parentId":1302,"tags":{},"startTime":1752396658465,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":299,"timestamp":514925920789,"id":1306,"parentId":1302,"tags":{},"startTime":1752396658465,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":39,"timestamp":514925921198,"id":1307,"parentId":1302,"tags":{},"startTime":1752396658465,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":37,"timestamp":514925921339,"id":1308,"parentId":1302,"tags":{},"startTime":1752396658465,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3517,"timestamp":514925920584,"id":1304,"parentId":1302,"tags":{},"startTime":1752396658465,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":932,"timestamp":514925927313,"id":1309,"parentId":1302,"tags":{},"startTime":1752396658471,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":9399,"timestamp":514925928289,"id":1310,"parentId":1302,"tags":{},"startTime":1752396658472,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":37956,"timestamp":514925945867,"id":1311,"parentId":1302,"tags":{},"startTime":1752396658490,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":419,"timestamp":514925983817,"id":1312,"parentId":1302,"tags":{},"startTime":1752396658528,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":300,"timestamp":514925984192,"id":1313,"parentId":1302,"tags":{},"startTime":1752396658528,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":9182,"timestamp":514925984516,"id":1314,"parentId":1302,"tags":{},"startTime":1752396658529,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1612,"timestamp":514925999375,"id":1316,"parentId":1288,"tags":{},"startTime":1752396658544,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":2503,"timestamp":514925998531,"id":1315,"parentId":1288,"tags":{},"startTime":1752396658543,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":98803,"timestamp":514925911111,"id":1302,"parentId":1288,"tags":{},"startTime":1752396658455,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":337768,"timestamp":514925672386,"id":1288,"parentId":1286,"tags":{"name":"client"},"startTime":1752396658217,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":44129,"timestamp":514926010386,"id":1317,"parentId":1286,"tags":{},"startTime":1752396658555,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":398491,"timestamp":514925659945,"id":1286,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396658204,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":8,"timestamp":514926071470,"id":1320,"parentId":3,"tags":{},"startTime":1752396658616,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":7481,"timestamp":514926094367,"id":1322,"parentId":1318,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"rsc"},"startTime":1752396658639,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":27661,"timestamp":514926079368,"id":1321,"parentId":1319,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396658624,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":479000,"timestamp":514925660234,"id":1330,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Footer.tsx"],"page":"/","isPageHidden":true},"startTime":1752396658685,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":14481,"timestamp":514926137305,"id":1329,"parentId":1318,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"ssr"},"startTime":1752396658681,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":89715,"timestamp":514926065547,"id":1319,"parentId":1318,"tags":{},"startTime":1752396658610,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":10617,"timestamp":514926248126,"id":1332,"parentId":1331,"tags":{},"startTime":1752396658792,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":12,"timestamp":514926258852,"id":1334,"parentId":1331,"tags":{},"startTime":1752396658803,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":8303,"timestamp":514926259049,"id":1335,"parentId":1331,"tags":{},"startTime":1752396658803,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":106,"timestamp":514926267891,"id":1336,"parentId":1331,"tags":{},"startTime":1752396658812,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":65,"timestamp":514926268327,"id":1337,"parentId":1331,"tags":{},"startTime":1752396658812,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":25328,"timestamp":514926258822,"id":1333,"parentId":1331,"tags":{},"startTime":1752396658803,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1303,"timestamp":514926286980,"id":1338,"parentId":1331,"tags":{},"startTime":1752396658831,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5258,"timestamp":514926288356,"id":1339,"parentId":1331,"tags":{},"startTime":1752396658832,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":8140,"timestamp":514926297627,"id":1340,"parentId":1331,"tags":{},"startTime":1752396658842,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":338,"timestamp":514926305761,"id":1341,"parentId":1331,"tags":{},"startTime":1752396658850,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":137,"timestamp":514926306072,"id":1342,"parentId":1331,"tags":{},"startTime":1752396658850,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":4205,"timestamp":514926306223,"id":1343,"parentId":1331,"tags":{},"startTime":1752396658850,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":72662,"timestamp":514926241935,"id":1331,"parentId":1318,"tags":{},"startTime":1752396658786,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":259620,"timestamp":514926063132,"id":1318,"parentId":1287,"tags":{"name":"server"},"startTime":1752396658607,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":34143,"timestamp":514926323179,"id":1344,"parentId":1287,"tags":{},"startTime":1752396658867,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":704329,"timestamp":514925660804,"id":1287,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396658205,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4766,"timestamp":514926380829,"id":1346,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396658925,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":3165,"timestamp":514926388793,"id":1347,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396658933,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":240517,"timestamp":514926375517,"id":1345,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396658920,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":7,"timestamp":514926616205,"id":1348,"parentId":1345,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"419840000","memory.heapUsed":"217876416","memory.heapTotal":"256045056"},"startTime":1752396659160,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":6516,"timestamp":514936793251,"id":1354,"parentId":1351,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"rsc"},"startTime":1752396669337,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":40764,"timestamp":514936767097,"id":1353,"parentId":1352,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396669311,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":8839,"timestamp":514936826576,"id":1361,"parentId":1351,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"ssr"},"startTime":1752396669371,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":84032,"timestamp":514936761531,"id":1352,"parentId":1351,"tags":{},"startTime":1752396669306,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":1908,"timestamp":514936849537,"id":1363,"parentId":1362,"tags":{},"startTime":1752396669394,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":6,"timestamp":514936851490,"id":1365,"parentId":1362,"tags":{},"startTime":1752396669396,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":7042,"timestamp":514936851641,"id":1366,"parentId":1362,"tags":{},"startTime":1752396669396,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":18,"timestamp":514936858765,"id":1367,"parentId":1362,"tags":{},"startTime":1752396669403,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":8,"timestamp":514936858812,"id":1368,"parentId":1362,"tags":{},"startTime":1752396669403,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":8404,"timestamp":514936851478,"id":1364,"parentId":1362,"tags":{},"startTime":1752396669396,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":739,"timestamp":514936862004,"id":1369,"parentId":1362,"tags":{},"startTime":1752396669406,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":2658,"timestamp":514936862785,"id":1370,"parentId":1362,"tags":{},"startTime":1752396669407,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":1819,"timestamp":514936866586,"id":1371,"parentId":1362,"tags":{},"startTime":1752396669411,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":125,"timestamp":514936868402,"id":1372,"parentId":1362,"tags":{},"startTime":1752396669413,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":90,"timestamp":514936868515,"id":1373,"parentId":1362,"tags":{},"startTime":1752396669413,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":2926,"timestamp":514936868614,"id":1374,"parentId":1362,"tags":{},"startTime":1752396669413,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":27474,"timestamp":514936848030,"id":1362,"parentId":1351,"tags":{},"startTime":1752396669392,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":118642,"timestamp":514936760881,"id":1351,"parentId":1349,"tags":{"name":"server"},"startTime":1752396669305,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":16568,"timestamp":514936879619,"id":1375,"parentId":1349,"tags":{},"startTime":1752396669424,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":145418,"timestamp":514936752194,"id":1349,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396669296,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":7112,"timestamp":514936908887,"id":1384,"parentId":1376,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"app-pages-browser"},"startTime":1752396669453,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":12832,"timestamp":514936903273,"id":1378,"parentId":1377,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396669447,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":22189,"timestamp":514936903384,"id":1381,"parentId":1377,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396669448,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":23442,"timestamp":514936903390,"id":1382,"parentId":1377,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396669448,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":37823,"timestamp":514936926916,"id":1387,"parentId":1386,"tags":{},"startTime":1752396669471,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":38173,"timestamp":514936926891,"id":1386,"parentId":1385,"tags":{},"startTime":1752396669471,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":18717,"timestamp":514936965121,"id":1388,"parentId":1385,"tags":{"astUsed":"true"},"startTime":1752396669509,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":83642,"timestamp":514936903366,"id":1379,"parentId":1377,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396669448,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":64590,"timestamp":514936922952,"id":1385,"parentId":1383,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396669467,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":86157,"timestamp":514936907941,"id":1383,"parentId":1376,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396669452,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":71,"timestamp":514936994410,"id":1389,"parentId":1383,"tags":{},"startTime":1752396669539,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":91144,"timestamp":514936903378,"id":1380,"parentId":1377,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396669448,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":95022,"timestamp":514936899540,"id":1377,"parentId":1376,"tags":{},"startTime":1752396669444,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":1065,"timestamp":514936997273,"id":1391,"parentId":1390,"tags":{},"startTime":1752396669541,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":5,"timestamp":514936998368,"id":1393,"parentId":1390,"tags":{},"startTime":1752396669543,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":50,"timestamp":514936998460,"id":1394,"parentId":1390,"tags":{},"startTime":1752396669543,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":5,"timestamp":514936998532,"id":1395,"parentId":1390,"tags":{},"startTime":1752396669543,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":4,"timestamp":514936998555,"id":1396,"parentId":1390,"tags":{},"startTime":1752396669543,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":1333,"timestamp":514936998360,"id":1392,"parentId":1390,"tags":{},"startTime":1752396669542,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1074,"timestamp":514937005608,"id":1397,"parentId":1390,"tags":{},"startTime":1752396669550,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3373,"timestamp":514937006762,"id":1398,"parentId":1390,"tags":{},"startTime":1752396669551,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3681,"timestamp":514937013074,"id":1399,"parentId":1390,"tags":{},"startTime":1752396669557,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":170,"timestamp":514937016751,"id":1400,"parentId":1390,"tags":{},"startTime":1752396669561,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":168,"timestamp":514937016909,"id":1401,"parentId":1390,"tags":{},"startTime":1752396669561,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":3882,"timestamp":514937017089,"id":1402,"parentId":1390,"tags":{},"startTime":1752396669561,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":237,"timestamp":514937023392,"id":1404,"parentId":1376,"tags":{},"startTime":1752396669568,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":512,"timestamp":514937023150,"id":1403,"parentId":1376,"tags":{},"startTime":1752396669567,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":30115,"timestamp":514936996124,"id":1390,"parentId":1376,"tags":{},"startTime":1752396669540,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":127128,"timestamp":514936899167,"id":1376,"parentId":1350,"tags":{"name":"client"},"startTime":1752396669443,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":42467,"timestamp":514937026344,"id":1405,"parentId":1350,"tags":{},"startTime":1752396669570,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":322012,"timestamp":514936752400,"id":1350,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396669297,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4504,"timestamp":514937082423,"id":1407,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396669627,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":6493,"timestamp":514937090939,"id":1408,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396669635,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":9,"timestamp":514937330974,"id":1409,"parentId":3,"tags":{},"startTime":1752396669875,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":270928,"timestamp":514937080565,"id":1406,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396669625,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":12,"timestamp":514937351658,"id":1410,"parentId":1406,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"449671168","memory.heapUsed":"235324376","memory.heapTotal":"270032896"},"startTime":1752396669896,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":663000,"timestamp":514936752090,"id":1411,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Footer.tsx"],"page":"/","isPageHidden":true},"startTime":1752396669961,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":11828,"timestamp":514992233152,"id":1417,"parentId":1414,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"rsc"},"startTime":1752396724787,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":55674,"timestamp":514992205589,"id":1416,"parentId":1415,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396724760,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":24031,"timestamp":514992296168,"id":1424,"parentId":1414,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"ssr"},"startTime":1752396724850,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":130221,"timestamp":514992198689,"id":1415,"parentId":1414,"tags":{},"startTime":1752396724753,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":5885,"timestamp":514992340306,"id":1426,"parentId":1425,"tags":{},"startTime":1752396724894,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":25,"timestamp":514992346317,"id":1428,"parentId":1425,"tags":{},"startTime":1752396724900,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":9758,"timestamp":514992346400,"id":1429,"parentId":1425,"tags":{},"startTime":1752396724901,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":44,"timestamp":514992356326,"id":1430,"parentId":1425,"tags":{},"startTime":1752396724910,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":31,"timestamp":514992356469,"id":1431,"parentId":1425,"tags":{},"startTime":1752396724911,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":13598,"timestamp":514992346283,"id":1427,"parentId":1425,"tags":{},"startTime":1752396724900,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":10647,"timestamp":514992364167,"id":1432,"parentId":1425,"tags":{},"startTime":1752396724918,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":6398,"timestamp":514992374940,"id":1433,"parentId":1425,"tags":{},"startTime":1752396724929,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":11871,"timestamp":514992390010,"id":1434,"parentId":1425,"tags":{},"startTime":1752396724944,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":488,"timestamp":514992401870,"id":1435,"parentId":1425,"tags":{},"startTime":1752396724956,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":294,"timestamp":514992402300,"id":1436,"parentId":1425,"tags":{},"startTime":1752396724956,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":9026,"timestamp":514992402647,"id":1437,"parentId":1425,"tags":{},"startTime":1752396724957,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":85684,"timestamp":514992331217,"id":1425,"parentId":1414,"tags":{},"startTime":1752396724885,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":232881,"timestamp":514992197753,"id":1414,"parentId":1412,"tags":{"name":"server"},"startTime":1752396724752,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":25878,"timestamp":514992430885,"id":1438,"parentId":1412,"tags":{},"startTime":1752396724985,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":280812,"timestamp":514992176848,"id":1412,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396724731,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":28890,"timestamp":514992478349,"id":1447,"parentId":1439,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"app-pages-browser"},"startTime":1752396725032,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":35577,"timestamp":514992471881,"id":1441,"parentId":1440,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396725026,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":40523,"timestamp":514992472038,"id":1444,"parentId":1440,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396725026,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":45378,"timestamp":514992472050,"id":1445,"parentId":1440,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396725026,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":77859,"timestamp":514992517773,"id":1450,"parentId":1449,"tags":{},"startTime":1752396725072,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":79566,"timestamp":514992517672,"id":1449,"parentId":1448,"tags":{},"startTime":1752396725072,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":57290,"timestamp":514992597397,"id":1451,"parentId":1448,"tags":{"astUsed":"true"},"startTime":1752396725152,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":192996,"timestamp":514992472008,"id":1442,"parentId":1440,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396725026,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":155556,"timestamp":514992510501,"id":1448,"parentId":1446,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396725065,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":197813,"timestamp":514992477455,"id":1446,"parentId":1439,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396725032,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":73,"timestamp":514992675560,"id":1452,"parentId":1446,"tags":{},"startTime":1752396725230,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":203646,"timestamp":514992472026,"id":1443,"parentId":1440,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396725026,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":215322,"timestamp":514992460395,"id":1440,"parentId":1439,"tags":{},"startTime":1752396725015,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":3497,"timestamp":514992680883,"id":1454,"parentId":1453,"tags":{},"startTime":1752396725235,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":9,"timestamp":514992684474,"id":1456,"parentId":1453,"tags":{},"startTime":1752396725239,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":148,"timestamp":514992684517,"id":1457,"parentId":1453,"tags":{},"startTime":1752396725239,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":12,"timestamp":514992684717,"id":1458,"parentId":1453,"tags":{},"startTime":1752396725239,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":13,"timestamp":514992684776,"id":1459,"parentId":1453,"tags":{},"startTime":1752396725239,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3058,"timestamp":514992684451,"id":1455,"parentId":1453,"tags":{},"startTime":1752396725239,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":861,"timestamp":514992689858,"id":1460,"parentId":1453,"tags":{},"startTime":1752396725244,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":4018,"timestamp":514992690760,"id":1461,"parentId":1453,"tags":{},"startTime":1752396725245,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":11033,"timestamp":514992697673,"id":1462,"parentId":1453,"tags":{},"startTime":1752396725252,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":297,"timestamp":514992708700,"id":1463,"parentId":1453,"tags":{},"startTime":1752396725263,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":213,"timestamp":514992708974,"id":1464,"parentId":1453,"tags":{},"startTime":1752396725263,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":17437,"timestamp":514992709203,"id":1465,"parentId":1453,"tags":{},"startTime":1752396725263,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":192,"timestamp":514992729152,"id":1467,"parentId":1439,"tags":{},"startTime":1752396725283,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":376,"timestamp":514992728988,"id":1466,"parentId":1439,"tags":{},"startTime":1752396725283,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":54922,"timestamp":514992678023,"id":1453,"parentId":1439,"tags":{},"startTime":1752396725232,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":273286,"timestamp":514992459790,"id":1439,"parentId":1413,"tags":{"name":"client"},"startTime":1752396725014,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":59667,"timestamp":514992733147,"id":1468,"parentId":1413,"tags":{},"startTime":1752396725287,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":622652,"timestamp":514992177087,"id":1413,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396724731,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4070,"timestamp":514992825631,"id":1470,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396725380,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":6848,"timestamp":514992833435,"id":1471,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396725388,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":13,"timestamp":514992972634,"id":1472,"parentId":3,"tags":{},"startTime":1752396725527,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":180084,"timestamp":514992822445,"id":1469,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396725377,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":11,"timestamp":514993002901,"id":1473,"parentId":1469,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"420888576","memory.heapUsed":"232424960","memory.heapTotal":"248492032"},"startTime":1752396725557,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":988000,"timestamp":514992180986,"id":1474,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Footer.tsx"],"page":"/","isPageHidden":true},"startTime":1752396725723,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10064,"timestamp":515108924848,"id":1480,"parentId":1477,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"rsc"},"startTime":1752396841496,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":48215,"timestamp":515108899394,"id":1479,"parentId":1478,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396841470,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":13328,"timestamp":515108985196,"id":1487,"parentId":1477,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"ssr"},"startTime":1752396841556,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":111356,"timestamp":515108893869,"id":1478,"parentId":1477,"tags":{},"startTime":1752396841465,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":8502,"timestamp":515109103060,"id":1489,"parentId":1488,"tags":{},"startTime":1752396841674,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":38,"timestamp":515109111721,"id":1491,"parentId":1488,"tags":{},"startTime":1752396841683,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":8819,"timestamp":515109111839,"id":1492,"parentId":1488,"tags":{},"startTime":1752396841683,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":64,"timestamp":515109121189,"id":1493,"parentId":1488,"tags":{},"startTime":1752396841692,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":45,"timestamp":515109121366,"id":1494,"parentId":1488,"tags":{},"startTime":1752396841692,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":15166,"timestamp":515109111674,"id":1490,"parentId":1488,"tags":{},"startTime":1752396841683,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1292,"timestamp":515109133594,"id":1495,"parentId":1488,"tags":{},"startTime":1752396841704,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5059,"timestamp":515109134944,"id":1496,"parentId":1488,"tags":{},"startTime":1752396841706,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3158,"timestamp":515109150284,"id":1497,"parentId":1488,"tags":{},"startTime":1752396841721,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":460,"timestamp":515109153435,"id":1498,"parentId":1488,"tags":{},"startTime":1752396841724,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":148,"timestamp":515109153871,"id":1499,"parentId":1488,"tags":{},"startTime":1752396841725,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":15170,"timestamp":515109154038,"id":1500,"parentId":1488,"tags":{},"startTime":1752396841725,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":82020,"timestamp":515109098383,"id":1488,"parentId":1477,"tags":{},"startTime":1752396841669,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":306740,"timestamp":515108893106,"id":1477,"parentId":1475,"tags":{"name":"server"},"startTime":1752396841464,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":26169,"timestamp":515109200058,"id":1501,"parentId":1475,"tags":{},"startTime":1752396841771,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":350613,"timestamp":515108877200,"id":1475,"parentId":3,"tags":{"trigger":"src/components/Services.tsx"},"startTime":1752396841448,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":13953,"timestamp":515109251414,"id":1510,"parentId":1502,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Services.tsx","layer":"app-pages-browser"},"startTime":1752396841822,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":23038,"timestamp":515109242508,"id":1504,"parentId":1503,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396841813,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":30560,"timestamp":515109242828,"id":1507,"parentId":1503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396841814,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":34044,"timestamp":515109242847,"id":1508,"parentId":1503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396841814,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":37383,"timestamp":515109277119,"id":1513,"parentId":1512,"tags":{},"startTime":1752396841848,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":37737,"timestamp":515109277073,"id":1512,"parentId":1511,"tags":{},"startTime":1752396841848,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":28194,"timestamp":515109314853,"id":1514,"parentId":1511,"tags":{"astUsed":"true"},"startTime":1752396841886,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":103843,"timestamp":515109242694,"id":1505,"parentId":1503,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396841814,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":76251,"timestamp":515109270772,"id":1511,"parentId":1509,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396841842,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":101767,"timestamp":515109250482,"id":1509,"parentId":1502,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396841821,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":87,"timestamp":515109354663,"id":1515,"parentId":1509,"tags":{},"startTime":1752396841926,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":112092,"timestamp":515109242714,"id":1506,"parentId":1503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396841814,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":123165,"timestamp":515109231695,"id":1503,"parentId":1502,"tags":{},"startTime":1752396841803,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":5771,"timestamp":515109419418,"id":1517,"parentId":1516,"tags":{},"startTime":1752396841990,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":13,"timestamp":515109425288,"id":1519,"parentId":1516,"tags":{},"startTime":1752396841996,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":165,"timestamp":515109425723,"id":1520,"parentId":1516,"tags":{},"startTime":1752396841997,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":16,"timestamp":515109425957,"id":1521,"parentId":1516,"tags":{},"startTime":1752396841997,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":13,"timestamp":515109426020,"id":1522,"parentId":1516,"tags":{},"startTime":1752396841997,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5598,"timestamp":515109425263,"id":1518,"parentId":1516,"tags":{},"startTime":1752396841996,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2682,"timestamp":515109437625,"id":1523,"parentId":1516,"tags":{},"startTime":1752396842009,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":15286,"timestamp":515109440453,"id":1524,"parentId":1516,"tags":{},"startTime":1752396842011,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":30280,"timestamp":515109464114,"id":1525,"parentId":1516,"tags":{},"startTime":1752396842035,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":285,"timestamp":515109494389,"id":1526,"parentId":1516,"tags":{},"startTime":1752396842065,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":346,"timestamp":515109494625,"id":1527,"parentId":1516,"tags":{},"startTime":1752396842066,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":7938,"timestamp":515109494992,"id":1528,"parentId":1516,"tags":{},"startTime":1752396842066,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1131,"timestamp":515109511243,"id":1530,"parentId":1502,"tags":{},"startTime":1752396842082,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":1832,"timestamp":515109510569,"id":1529,"parentId":1502,"tags":{},"startTime":1752396842081,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":99731,"timestamp":515109415646,"id":1516,"parentId":1502,"tags":{},"startTime":1752396841987,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":284415,"timestamp":515109231081,"id":1502,"parentId":1476,"tags":{"name":"client"},"startTime":1752396841802,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":46335,"timestamp":515109515618,"id":1531,"parentId":1476,"tags":{},"startTime":1752396842087,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":695039,"timestamp":515108877641,"id":1476,"parentId":3,"tags":{"trigger":"src/components/Services.tsx"},"startTime":1752396841449,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"client-success","duration":11,"timestamp":515109591772,"id":1534,"parentId":3,"tags":{},"startTime":1752396842163,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4595,"timestamp":515109588986,"id":1533,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396842160,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4188,"timestamp":515109598236,"id":1535,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396842169,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":249222,"timestamp":515109580411,"id":1532,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396842151,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":6,"timestamp":515109829794,"id":1536,"parentId":1532,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"432173056","memory.heapUsed":"228094632","memory.heapTotal":"268939264"},"startTime":1752396842401,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":996000,"timestamp":515108879376,"id":1537,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Services.tsx"],"page":"/","isPageHidden":true},"startTime":1752396842447,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10312,"timestamp":515169822167,"id":1543,"parentId":1540,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752396902402,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":33858,"timestamp":515169803860,"id":1542,"parentId":1541,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396902384,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":12973,"timestamp":515169863099,"id":1550,"parentId":1540,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752396902443,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":85904,"timestamp":515169797792,"id":1541,"parentId":1540,"tags":{},"startTime":1752396902378,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":12301,"timestamp":515169980386,"id":1552,"parentId":1551,"tags":{},"startTime":1752396902561,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":74,"timestamp":515169992985,"id":1554,"parentId":1551,"tags":{},"startTime":1752396902573,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":9393,"timestamp":515169993281,"id":1555,"parentId":1551,"tags":{},"startTime":1752396902574,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":71,"timestamp":515170002887,"id":1556,"parentId":1551,"tags":{},"startTime":1752396902583,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":50,"timestamp":515170003070,"id":1557,"parentId":1551,"tags":{},"startTime":1752396902583,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":13464,"timestamp":515169992843,"id":1553,"parentId":1551,"tags":{},"startTime":1752396902573,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2206,"timestamp":515170010776,"id":1558,"parentId":1551,"tags":{},"startTime":1752396902591,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":6703,"timestamp":515170013049,"id":1559,"parentId":1551,"tags":{},"startTime":1752396902593,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":4091,"timestamp":515170032955,"id":1560,"parentId":1551,"tags":{},"startTime":1752396902613,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":395,"timestamp":515170037040,"id":1561,"parentId":1551,"tags":{},"startTime":1752396902617,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":132,"timestamp":515170037413,"id":1562,"parentId":1551,"tags":{},"startTime":1752396902618,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":3699,"timestamp":515170037556,"id":1563,"parentId":1551,"tags":{},"startTime":1752396902618,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":79249,"timestamp":515169967299,"id":1551,"parentId":1540,"tags":{},"startTime":1752396902548,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":258813,"timestamp":515169794377,"id":1540,"parentId":1538,"tags":{"name":"server"},"startTime":1752396902375,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":34143,"timestamp":515170053438,"id":1564,"parentId":1538,"tags":{},"startTime":1752396902634,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":310823,"timestamp":515169778664,"id":1538,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396902359,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":15882,"timestamp":515170103077,"id":1567,"parentId":1566,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396902683,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":26990,"timestamp":515170113999,"id":1573,"parentId":1565,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396902694,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":48012,"timestamp":515170103399,"id":1570,"parentId":1566,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396902684,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":49787,"timestamp":515170103406,"id":1571,"parentId":1566,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396902684,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":84311,"timestamp":515170153576,"id":1576,"parentId":1575,"tags":{},"startTime":1752396902734,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":85319,"timestamp":515170153504,"id":1575,"parentId":1574,"tags":{},"startTime":1752396902734,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":51437,"timestamp":515170238937,"id":1577,"parentId":1574,"tags":{"astUsed":"true"},"startTime":1752396902819,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":195381,"timestamp":515170103371,"id":1568,"parentId":1566,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396902684,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":154072,"timestamp":515170145443,"id":1574,"parentId":1572,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396902726,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":202274,"timestamp":515170109757,"id":1572,"parentId":1565,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396902690,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":68,"timestamp":515170312624,"id":1578,"parentId":1572,"tags":{},"startTime":1752396902893,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":209342,"timestamp":515170103391,"id":1569,"parentId":1566,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396902684,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":217947,"timestamp":515170094830,"id":1566,"parentId":1565,"tags":{},"startTime":1752396902675,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":13437,"timestamp":515170383894,"id":1580,"parentId":1579,"tags":{},"startTime":1752396902964,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":32,"timestamp":515170397527,"id":1582,"parentId":1579,"tags":{},"startTime":1752396902978,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":770,"timestamp":515170397922,"id":1583,"parentId":1579,"tags":{},"startTime":1752396902978,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":53,"timestamp":515170398963,"id":1584,"parentId":1579,"tags":{},"startTime":1752396902979,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":54,"timestamp":515170399285,"id":1585,"parentId":1579,"tags":{},"startTime":1752396902980,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":7227,"timestamp":515170397471,"id":1581,"parentId":1579,"tags":{},"startTime":1752396902978,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2350,"timestamp":515170416392,"id":1586,"parentId":1579,"tags":{},"startTime":1752396902997,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":14097,"timestamp":515170418855,"id":1587,"parentId":1579,"tags":{},"startTime":1752396902999,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":14390,"timestamp":515170441509,"id":1588,"parentId":1579,"tags":{},"startTime":1752396903022,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":455,"timestamp":515170455894,"id":1589,"parentId":1579,"tags":{},"startTime":1752396903036,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":375,"timestamp":515170456311,"id":1590,"parentId":1579,"tags":{},"startTime":1752396903037,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":43936,"timestamp":515170456716,"id":1591,"parentId":1579,"tags":{},"startTime":1752396903037,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":2521,"timestamp":515170510504,"id":1593,"parentId":1565,"tags":{},"startTime":1752396903091,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":4214,"timestamp":515170508862,"id":1592,"parentId":1565,"tags":{},"startTime":1752396903089,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":144794,"timestamp":515170376649,"id":1579,"parentId":1565,"tags":{},"startTime":1752396902957,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":429759,"timestamp":515170091960,"id":1565,"parentId":1539,"tags":{"name":"client"},"startTime":1752396902672,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":55860,"timestamp":515170522076,"id":1594,"parentId":1539,"tags":{},"startTime":1752396903102,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":813691,"timestamp":515169779270,"id":1539,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396902360,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":4506,"timestamp":515170614635,"id":1596,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396903195,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":4654,"timestamp":515170627170,"id":1597,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396903208,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":14,"timestamp":515170896143,"id":1598,"parentId":3,"tags":{},"startTime":1752396903476,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":314073,"timestamp":515170605415,"id":1595,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396903186,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":11,"timestamp":515170919842,"id":1599,"parentId":1595,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"443580416","memory.heapUsed":"244404024","memory.heapTotal":"274325504"},"startTime":1752396903500,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":1262000,"timestamp":515169782878,"id":1600,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752396903625,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10290,"timestamp":515186906594,"id":1606,"parentId":1603,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"rsc"},"startTime":1752396919487,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":39962,"timestamp":515186884599,"id":1605,"parentId":1604,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396919465,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10305,"timestamp":515186943835,"id":1613,"parentId":1603,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"ssr"},"startTime":1752396919524,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":80681,"timestamp":515186879435,"id":1604,"parentId":1603,"tags":{},"startTime":1752396919460,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":1830,"timestamp":515186968083,"id":1615,"parentId":1614,"tags":{},"startTime":1752396919548,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":8,"timestamp":515186969965,"id":1617,"parentId":1614,"tags":{},"startTime":1752396919550,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":2677,"timestamp":515186970152,"id":1618,"parentId":1614,"tags":{},"startTime":1752396919550,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":13,"timestamp":515186972886,"id":1619,"parentId":1614,"tags":{},"startTime":1752396919553,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":10,"timestamp":515186972931,"id":1620,"parentId":1614,"tags":{},"startTime":1752396919553,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":5345,"timestamp":515186969949,"id":1616,"parentId":1614,"tags":{},"startTime":1752396919550,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1159,"timestamp":515186978259,"id":1621,"parentId":1614,"tags":{},"startTime":1752396919559,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":5347,"timestamp":515186979485,"id":1622,"parentId":1614,"tags":{},"startTime":1752396919560,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":3351,"timestamp":515186995284,"id":1623,"parentId":1614,"tags":{},"startTime":1752396919576,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":204,"timestamp":515186998631,"id":1624,"parentId":1614,"tags":{},"startTime":1752396919579,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":132,"timestamp":515186998814,"id":1625,"parentId":1614,"tags":{},"startTime":1752396919579,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":2873,"timestamp":515186998958,"id":1626,"parentId":1614,"tags":{},"startTime":1752396919579,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":39954,"timestamp":515186966547,"id":1614,"parentId":1603,"tags":{},"startTime":1752396919547,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":140119,"timestamp":515186878973,"id":1603,"parentId":1601,"tags":{"name":"server"},"startTime":1752396919459,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":21832,"timestamp":515187019230,"id":1627,"parentId":1601,"tags":{},"startTime":1752396919600,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":181216,"timestamp":515186863720,"id":1601,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396919444,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":12134,"timestamp":515187055972,"id":1636,"parentId":1628,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\About.tsx","layer":"app-pages-browser"},"startTime":1752396919636,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":17035,"timestamp":515187051252,"id":1630,"parentId":1629,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396919632,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":21021,"timestamp":515187051349,"id":1633,"parentId":1629,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396919632,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":27569,"timestamp":515187051354,"id":1634,"parentId":1629,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396919632,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":33825,"timestamp":515187079096,"id":1639,"parentId":1638,"tags":{},"startTime":1752396919659,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":34368,"timestamp":515187079050,"id":1638,"parentId":1637,"tags":{},"startTime":1752396919659,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":17706,"timestamp":515187113480,"id":1640,"parentId":1637,"tags":{"astUsed":"true"},"startTime":1752396919694,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":83439,"timestamp":515187051334,"id":1631,"parentId":1629,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396919632,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":64364,"timestamp":515187070655,"id":1637,"parentId":1635,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396919651,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":82968,"timestamp":515187055545,"id":1635,"parentId":1628,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396919636,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":33,"timestamp":515187138629,"id":1641,"parentId":1635,"tags":{},"startTime":1752396919719,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":87334,"timestamp":515187051344,"id":1632,"parentId":1629,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396919632,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":91044,"timestamp":515187047652,"id":1629,"parentId":1628,"tags":{},"startTime":1752396919628,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":2966,"timestamp":515187141211,"id":1643,"parentId":1642,"tags":{},"startTime":1752396919722,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":12,"timestamp":515187144281,"id":1645,"parentId":1642,"tags":{},"startTime":1752396919725,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":190,"timestamp":515187144570,"id":1646,"parentId":1642,"tags":{},"startTime":1752396919725,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":21,"timestamp":515187144836,"id":1647,"parentId":1642,"tags":{},"startTime":1752396919725,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":11,"timestamp":515187144906,"id":1648,"parentId":1642,"tags":{},"startTime":1752396919725,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2025,"timestamp":515187144255,"id":1644,"parentId":1642,"tags":{},"startTime":1752396919725,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":777,"timestamp":515187147997,"id":1649,"parentId":1642,"tags":{},"startTime":1752396919728,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3718,"timestamp":515187148832,"id":1650,"parentId":1642,"tags":{},"startTime":1752396919729,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":13521,"timestamp":515187154077,"id":1651,"parentId":1642,"tags":{},"startTime":1752396919734,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":292,"timestamp":515187167589,"id":1652,"parentId":1642,"tags":{},"startTime":1752396919748,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":330,"timestamp":515187167854,"id":1653,"parentId":1642,"tags":{},"startTime":1752396919748,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":6037,"timestamp":515187168207,"id":1654,"parentId":1642,"tags":{},"startTime":1752396919749,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":223,"timestamp":515187179256,"id":1656,"parentId":1628,"tags":{},"startTime":1752396919760,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":565,"timestamp":515187178958,"id":1655,"parentId":1628,"tags":{},"startTime":1752396919759,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":44838,"timestamp":515187139762,"id":1642,"parentId":1628,"tags":{},"startTime":1752396919720,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":137569,"timestamp":515187047168,"id":1628,"parentId":1602,"tags":{"name":"client"},"startTime":1752396919627,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":61101,"timestamp":515187184843,"id":1657,"parentId":1602,"tags":{},"startTime":1752396919765,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":388463,"timestamp":515186863899,"id":1602,"parentId":3,"tags":{"trigger":"src/components/About.tsx"},"startTime":1752396919444,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"ensure-page","duration":5002,"timestamp":515187264770,"id":1659,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396919845,"traceId":"d09f67b8e21bcf6b"},{"name":"ensure-page","duration":5063,"timestamp":515187274561,"id":1660,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1752396919855,"traceId":"d09f67b8e21bcf6b"},{"name":"client-success","duration":7,"timestamp":515187424296,"id":1661,"parentId":3,"tags":{},"startTime":1752396920005,"traceId":"d09f67b8e21bcf6b"},{"name":"handle-request","duration":171061,"timestamp":515187261792,"id":1658,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1752396919842,"traceId":"d09f67b8e21bcf6b"},{"name":"memory-usage","duration":5,"timestamp":515187432955,"id":1662,"parentId":1658,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"467726336","memory.heapUsed":"250161640","memory.heapTotal":"287883264"},"startTime":1752396920013,"traceId":"d09f67b8e21bcf6b"},{"name":"client-hmr-latency","duration":650000,"timestamp":515186867146,"id":1663,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx"],"page":"/","isPageHidden":true},"startTime":1752396920097,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":5650,"timestamp":515197372502,"id":1669,"parentId":1666,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"rsc"},"startTime":1752396929953,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":30401,"timestamp":515197355724,"id":1668,"parentId":1667,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1752396929936,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":9278,"timestamp":515197403938,"id":1676,"parentId":1666,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"ssr"},"startTime":1752396929984,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":68212,"timestamp":515197350521,"id":1667,"parentId":1666,"tags":{},"startTime":1752396929931,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":1748,"timestamp":515197425242,"id":1678,"parentId":1677,"tags":{},"startTime":1752396930006,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":6,"timestamp":515197427048,"id":1680,"parentId":1677,"tags":{},"startTime":1752396930007,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":1487,"timestamp":515197427071,"id":1681,"parentId":1677,"tags":{},"startTime":1752396930007,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":15,"timestamp":515197428632,"id":1682,"parentId":1677,"tags":{},"startTime":1752396930009,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":9,"timestamp":515197428678,"id":1683,"parentId":1677,"tags":{},"startTime":1752396930009,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":2716,"timestamp":515197427034,"id":1679,"parentId":1677,"tags":{},"startTime":1752396930007,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":1100,"timestamp":515197437659,"id":1684,"parentId":1677,"tags":{},"startTime":1752396930018,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":3510,"timestamp":515197438864,"id":1685,"parentId":1677,"tags":{},"startTime":1752396930019,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":1305,"timestamp":515197443663,"id":1686,"parentId":1677,"tags":{},"startTime":1752396930024,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":454,"timestamp":515197444965,"id":1687,"parentId":1677,"tags":{},"startTime":1752396930025,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":217,"timestamp":515197445381,"id":1688,"parentId":1677,"tags":{},"startTime":1752396930026,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":2513,"timestamp":515197445627,"id":1689,"parentId":1677,"tags":{},"startTime":1752396930026,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":32247,"timestamp":515197422788,"id":1677,"parentId":1666,"tags":{},"startTime":1752396930003,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":111223,"timestamp":515197349750,"id":1666,"parentId":1664,"tags":{"name":"server"},"startTime":1752396929930,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":25958,"timestamp":515197461122,"id":1690,"parentId":1664,"tags":{},"startTime":1752396930041,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-server","duration":154708,"timestamp":515197334065,"id":1664,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396929914,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":9499,"timestamp":515197499104,"id":1693,"parentId":1692,"tags":{"request":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1752396930079,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-tsx","duration":10789,"timestamp":515197505146,"id":1699,"parentId":1691,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\components\\Footer.tsx","layer":"app-pages-browser"},"startTime":1752396930085,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":18916,"timestamp":515197499576,"id":1696,"parentId":1692,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Ccomponents%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1752396930080,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":22116,"timestamp":515197499708,"id":1697,"parentId":1692,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396930080,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-process","duration":40026,"timestamp":515197522833,"id":1702,"parentId":1701,"tags":{},"startTime":1752396930103,"traceId":"d09f67b8e21bcf6b"},{"name":"postcss-loader","duration":45119,"timestamp":515197522780,"id":1701,"parentId":1700,"tags":{},"startTime":1752396930103,"traceId":"d09f67b8e21bcf6b"},{"name":"css-loader","duration":16198,"timestamp":515197568004,"id":1703,"parentId":1700,"tags":{"astUsed":"true"},"startTime":1752396930148,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":89689,"timestamp":515197499528,"id":1694,"parentId":1692,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1752396930080,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":79450,"timestamp":515197510483,"id":1700,"parentId":1698,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":null},"startTime":1752396930091,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module-css","duration":96957,"timestamp":515197504396,"id":1698,"parentId":1691,"tags":{"name":"C:\\Users\\<USER>\\Desktop\\Residential Rehab\\Residential_Rehab\\Residential\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1752396930085,"traceId":"d09f67b8e21bcf6b"},{"name":"build-module","duration":102,"timestamp":515197602080,"id":1704,"parentId":1698,"tags":{},"startTime":1752396930182,"traceId":"d09f67b8e21bcf6b"},{"name":"add-entry","duration":102701,"timestamp":515197499557,"id":1695,"parentId":1692,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CResidential%20Rehab%5C%5CResidential_Rehab%5C%5CResidential%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1752396930080,"traceId":"d09f67b8e21bcf6b"},{"name":"make","duration":110679,"timestamp":515197491653,"id":1692,"parentId":1691,"tags":{},"startTime":1752396930072,"traceId":"d09f67b8e21bcf6b"},{"name":"chunk-graph","duration":11966,"timestamp":515197611765,"id":1706,"parentId":1705,"tags":{},"startTime":1752396930192,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-modules","duration":37,"timestamp":515197623924,"id":1708,"parentId":1705,"tags":{},"startTime":1752396930204,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunks","duration":242,"timestamp":515197624047,"id":1709,"parentId":1705,"tags":{},"startTime":1752396930204,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-tree","duration":44,"timestamp":515197624404,"id":1710,"parentId":1705,"tags":{},"startTime":1752396930205,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize-chunk-modules","duration":36,"timestamp":515197624545,"id":1711,"parentId":1705,"tags":{},"startTime":1752396930205,"traceId":"d09f67b8e21bcf6b"},{"name":"optimize","duration":3423,"timestamp":515197623871,"id":1707,"parentId":1705,"tags":{},"startTime":1752396930204,"traceId":"d09f67b8e21bcf6b"},{"name":"module-hash","duration":2238,"timestamp":515197632894,"id":1712,"parentId":1705,"tags":{},"startTime":1752396930213,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation","duration":9635,"timestamp":515197635248,"id":1713,"parentId":1705,"tags":{},"startTime":1752396930216,"traceId":"d09f67b8e21bcf6b"},{"name":"hash","duration":19820,"timestamp":515197655948,"id":1714,"parentId":1705,"tags":{},"startTime":1752396930236,"traceId":"d09f67b8e21bcf6b"},{"name":"code-generation-jobs","duration":514,"timestamp":515197675761,"id":1715,"parentId":1705,"tags":{},"startTime":1752396930256,"traceId":"d09f67b8e21bcf6b"},{"name":"module-assets","duration":779,"timestamp":515197676232,"id":1716,"parentId":1705,"tags":{},"startTime":1752396930257,"traceId":"d09f67b8e21bcf6b"},{"name":"create-chunk-assets","duration":41046,"timestamp":515197677082,"id":1717,"parentId":1705,"tags":{},"startTime":1752396930257,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-generateClientManifest","duration":214,"timestamp":515197722873,"id":1719,"parentId":1691,"tags":{},"startTime":1752396930303,"traceId":"d09f67b8e21bcf6b"},{"name":"NextJsBuildManifest-createassets","duration":618,"timestamp":515197722504,"id":1718,"parentId":1691,"tags":{},"startTime":1752396930303,"traceId":"d09f67b8e21bcf6b"},{"name":"seal","duration":122032,"timestamp":515197606993,"id":1705,"parentId":1691,"tags":{},"startTime":1752396930187,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-compilation","duration":238022,"timestamp":515197491146,"id":1691,"parentId":1665,"tags":{"name":"client"},"startTime":1752396930071,"traceId":"d09f67b8e21bcf6b"},{"name":"emit","duration":91813,"timestamp":515197729344,"id":1720,"parentId":1665,"tags":{},"startTime":1752396930310,"traceId":"d09f67b8e21bcf6b"},{"name":"webpack-invalidated-client","duration":493423,"timestamp":515197334367,"id":1665,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1752396929915,"traceId":"d09f67b8e21bcf6b"}]
[{"name":"hot-reloader","duration":76,"timestamp":1124194150478,"id":3,"tags":{"version":"15.3.5"},"startTime":1753005847101,"traceId":"1379839007c9251f"},{"name":"setup-dev-bundler","duration":702041,"timestamp":1124193966272,"id":2,"parentId":1,"tags":{},"startTime":1753005846916,"traceId":"1379839007c9251f"},{"name":"run-instrumentation-hook","duration":25,"timestamp":1124194729570,"id":4,"parentId":1,"tags":{},"startTime":1753005847680,"traceId":"1379839007c9251f"},{"name":"start-dev-server","duration":1303932,"timestamp":1124193447637,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18039070720","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"179920896","memory.heapTotal":"102133760","memory.heapUsed":"63985256"},"startTime":1753005846398,"traceId":"1379839007c9251f"},{"name":"compile-path","duration":4518685,"timestamp":1124205417575,"id":7,"tags":{"trigger":"/"},"startTime":1753005858368,"traceId":"1379839007c9251f"},{"name":"ensure-page","duration":4520689,"timestamp":1124205416361,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753005858366,"traceId":"1379839007c9251f"}]
[{"name":"ensure-page","duration":50934,"timestamp":1124209948577,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753005862899,"traceId":"1379839007c9251f"},{"name":"handle-request","duration":5279156,"timestamp":1124205403732,"id":5,"tags":{"url":"/"},"startTime":1753005858354,"traceId":"1379839007c9251f"},{"name":"memory-usage","duration":62,"timestamp":1124210683030,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"446750720","memory.heapUsed":"82027784","memory.heapTotal":"97611776"},"startTime":1753005863633,"traceId":"1379839007c9251f"},{"name":"compile-path","duration":626353,"timestamp":1124211523592,"id":12,"tags":{"trigger":"/favicon.ico"},"startTime":1753005864474,"traceId":"1379839007c9251f"}]
[{"name":"ensure-page","duration":61321,"timestamp":1124212158579,"id":13,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1753005865109,"traceId":"1379839007c9251f"},{"name":"handle-request","duration":1031413,"timestamp":1124211519046,"id":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1753005864469,"traceId":"1379839007c9251f"},{"name":"memory-usage","duration":8,"timestamp":1124212550562,"id":14,"parentId":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"479232000","memory.heapUsed":"89655712","memory.heapTotal":"104194048"},"startTime":1753005865501,"traceId":"1379839007c9251f"},{"name":"ensure-page","duration":1143,"timestamp":1124238544199,"id":15,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005891494,"traceId":"1379839007c9251f"},{"name":"ensure-page","duration":536,"timestamp":1124238545444,"id":16,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005891495,"traceId":"1379839007c9251f"},{"name":"ensure-page","duration":490,"timestamp":1124238547523,"id":17,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005891498,"traceId":"1379839007c9251f"},{"name":"ensure-page","duration":513,"timestamp":1124238548072,"id":18,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005891498,"traceId":"1379839007c9251f"},{"name":"compile-path","duration":781181,"timestamp":1124238553828,"id":21,"tags":{"trigger":"/_not-found/page"},"startTime":1753005891504,"traceId":"1379839007c9251f"}]
[{"name":"ensure-page","duration":58546,"timestamp":1124239336801,"id":22,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753005892287,"traceId":"1379839007c9251f"},{"name":"handle-request","duration":934043,"timestamp":1124238549483,"id":19,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005891500,"traceId":"1379839007c9251f"},{"name":"memory-usage","duration":32,"timestamp":1124239483707,"id":23,"parentId":19,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"491483136","memory.heapUsed":"85678632","memory.heapTotal":"92438528"},"startTime":1753005892434,"traceId":"1379839007c9251f"},{"name":"compile-path","duration":794004,"timestamp":1124243503381,"id":26,"tags":{"trigger":"/api/consultation"},"startTime":1753005896453,"traceId":"1379839007c9251f"}]
[{"name":"compile-path","duration":1518607,"timestamp":1124244317206,"id":28,"tags":{"trigger":"/_error"},"startTime":1753005897267,"traceId":"1379839007c9251f"}]
[{"name":"next-dev","duration":62015204,"timestamp":1124192407096,"id":1,"tags":{},"startTime":1753005845353,"traceId":"1379839007c9251f"}]
[{"name":"hot-reloader","duration":56,"timestamp":1124272761901,"id":3,"tags":{"version":"15.3.5"},"startTime":1753005925716,"traceId":"dc82b8e5a6f8340b"},{"name":"setup-dev-bundler","duration":533717,"timestamp":1124272588428,"id":2,"parentId":1,"tags":{},"startTime":1753005925543,"traceId":"dc82b8e5a6f8340b"},{"name":"run-instrumentation-hook","duration":20,"timestamp":1124273159817,"id":4,"parentId":1,"tags":{},"startTime":1753005926114,"traceId":"dc82b8e5a6f8340b"},{"name":"start-dev-server","duration":993252,"timestamp":1124272176177,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"17685798912","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"175980544","memory.heapTotal":"96104448","memory.heapUsed":"69472144"},"startTime":1753005925131,"traceId":"dc82b8e5a6f8340b"},{"name":"compile-path","duration":4033823,"timestamp":1124307190010,"id":7,"tags":{"trigger":"/"},"startTime":1753005960144,"traceId":"dc82b8e5a6f8340b"},{"name":"ensure-page","duration":4035259,"timestamp":1124307189392,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753005960144,"traceId":"dc82b8e5a6f8340b"}]
[{"name":"ensure-page","duration":58746,"timestamp":1124311233592,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753005964188,"traceId":"dc82b8e5a6f8340b"},{"name":"handle-request","duration":4694139,"timestamp":1124307182525,"id":5,"tags":{"url":"/"},"startTime":1753005960137,"traceId":"dc82b8e5a6f8340b"},{"name":"memory-usage","duration":20,"timestamp":1124311876879,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"433115136","memory.heapUsed":"83104848","memory.heapTotal":"90271744"},"startTime":1753005964831,"traceId":"dc82b8e5a6f8340b"},{"name":"ensure-page","duration":2961,"timestamp":1124311901673,"id":10,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005964856,"traceId":"dc82b8e5a6f8340b"},{"name":"ensure-page","duration":470,"timestamp":1124311904933,"id":11,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005964859,"traceId":"dc82b8e5a6f8340b"},{"name":"ensure-page","duration":653,"timestamp":1124311910681,"id":12,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005964865,"traceId":"dc82b8e5a6f8340b"},{"name":"ensure-page","duration":804,"timestamp":1124311911445,"id":13,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005964866,"traceId":"dc82b8e5a6f8340b"},{"name":"compile-path","duration":881558,"timestamp":1124311918312,"id":16,"tags":{"trigger":"/_not-found/page"},"startTime":1753005964873,"traceId":"dc82b8e5a6f8340b"}]
[{"name":"ensure-page","duration":99758,"timestamp":1124312801725,"id":17,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753005965756,"traceId":"dc82b8e5a6f8340b"},{"name":"handle-request","duration":1064074,"timestamp":1124311913387,"id":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753005964868,"traceId":"dc82b8e5a6f8340b"},{"name":"memory-usage","duration":18,"timestamp":1124312977568,"id":18,"parentId":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"475815936","memory.heapUsed":"84444944","memory.heapTotal":"101019648"},"startTime":1753005965932,"traceId":"dc82b8e5a6f8340b"},{"name":"compile-path","duration":788071,"timestamp":1124342223840,"id":21,"tags":{"trigger":"/api/consultation"},"startTime":1753005995181,"traceId":"dc82b8e5a6f8340b"}]
[{"name":"ensure-page","duration":55306,"timestamp":1124343019448,"id":22,"parentId":3,"tags":{"inputPage":"/api/consultation/route"},"startTime":1753005995977,"traceId":"dc82b8e5a6f8340b"},{"name":"compile-path","duration":1522710,"timestamp":1124344200219,"id":24,"tags":{"trigger":"/_error"},"startTime":1753005997158,"traceId":"dc82b8e5a6f8340b"}]
[{"name":"next-dev","duration":853900672,"timestamp":1124271506296,"id":1,"tags":{},"startTime":1753005924460,"traceId":"dc82b8e5a6f8340b"}]
[{"name":"hot-reloader","duration":58,"timestamp":1127215915064,"id":3,"tags":{"version":"15.3.5"},"startTime":1753008868962,"traceId":"00e33c493de06f82"},{"name":"setup-dev-bundler","duration":555712,"timestamp":1127215737391,"id":2,"parentId":1,"tags":{},"startTime":1753008868785,"traceId":"00e33c493de06f82"},{"name":"run-instrumentation-hook","duration":20,"timestamp":1127216333359,"id":4,"parentId":1,"tags":{},"startTime":1753008869381,"traceId":"00e33c493de06f82"},{"name":"start-dev-server","duration":1069690,"timestamp":1127215281505,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"17309282304","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"176553984","memory.heapTotal":"96366592","memory.heapUsed":"70939144"},"startTime":1753008868329,"traceId":"00e33c493de06f82"},{"name":"compile-path","duration":623219,"timestamp":1127248807357,"id":7,"tags":{"trigger":"/api/consultation"},"startTime":1753008901855,"traceId":"00e33c493de06f82"},{"name":"ensure-page","duration":624485,"timestamp":1127248806761,"id":6,"parentId":3,"tags":{"inputPage":"/api/consultation/route"},"startTime":1753008901854,"traceId":"00e33c493de06f82"}]
[{"name":"ensure-page","duration":34120,"timestamp":1127249436519,"id":8,"parentId":3,"tags":{"inputPage":"/api/consultation/route"},"startTime":1753008902484,"traceId":"00e33c493de06f82"},{"name":"handle-request","duration":2522130,"timestamp":1127248801930,"id":5,"tags":{"url":"/api/consultation"},"startTime":1753008901849,"traceId":"00e33c493de06f82"},{"name":"memory-usage","duration":9,"timestamp":1127251324139,"id":9,"parentId":5,"tags":{"url":"/api/consultation","memory.rss":"362352640","memory.heapUsed":"74855864","memory.heapTotal":"85319680"},"startTime":1753008904371,"traceId":"00e33c493de06f82"},{"name":"compile-path","duration":1707153,"timestamp":1127363128783,"id":12,"tags":{"trigger":"/"},"startTime":1753009016179,"traceId":"00e33c493de06f82"}]
[{"name":"ensure-page","duration":45067,"timestamp":1127364841016,"id":13,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753009017892,"traceId":"00e33c493de06f82"},{"name":"handle-request","duration":2105881,"timestamp":1127363122163,"id":10,"tags":{"url":"/"},"startTime":1753009016173,"traceId":"00e33c493de06f82"},{"name":"memory-usage","duration":13,"timestamp":1127365228204,"id":14,"parentId":10,"tags":{"url":"/","memory.rss":"471478272","memory.heapUsed":"84861744","memory.heapTotal":"95698944"},"startTime":1753009018279,"traceId":"00e33c493de06f82"},{"name":"ensure-page","duration":1450,"timestamp":1127365260688,"id":15,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009018311,"traceId":"00e33c493de06f82"},{"name":"ensure-page","duration":538,"timestamp":1127365262296,"id":16,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009018313,"traceId":"00e33c493de06f82"},{"name":"ensure-page","duration":641,"timestamp":1127365265821,"id":17,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009018316,"traceId":"00e33c493de06f82"},{"name":"ensure-page","duration":589,"timestamp":1127365266550,"id":18,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009018317,"traceId":"00e33c493de06f82"},{"name":"compile-path","duration":812871,"timestamp":1127365272242,"id":21,"tags":{"trigger":"/_not-found/page"},"startTime":1753009018323,"traceId":"00e33c493de06f82"}]
[{"name":"next-dev","duration":410044446,"timestamp":1127214567270,"id":1,"tags":{},"startTime":1753008867614,"traceId":"00e33c493de06f82"}]
[{"name":"hot-reloader","duration":56,"timestamp":1127668712894,"id":3,"tags":{"version":"15.3.5"},"startTime":1753009321769,"traceId":"7029ce625923b151"},{"name":"setup-dev-bundler","duration":553004,"timestamp":1127668537395,"id":2,"parentId":1,"tags":{},"startTime":1753009321593,"traceId":"7029ce625923b151"},{"name":"run-instrumentation-hook","duration":18,"timestamp":1127669133059,"id":4,"parentId":1,"tags":{},"startTime":1753009322189,"traceId":"7029ce625923b151"},{"name":"start-dev-server","duration":1029461,"timestamp":1127668118618,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"17832230912","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"179838976","memory.heapTotal":"101609472","memory.heapUsed":"63719616"},"startTime":1753009321174,"traceId":"7029ce625923b151"},{"name":"compile-path","duration":2072128,"timestamp":1127675460140,"id":7,"tags":{"trigger":"/"},"startTime":1753009328516,"traceId":"7029ce625923b151"},{"name":"ensure-page","duration":2073674,"timestamp":1127675459545,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753009328515,"traceId":"7029ce625923b151"}]
[{"name":"ensure-page","duration":59903,"timestamp":1127677547230,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753009330603,"traceId":"7029ce625923b151"},{"name":"handle-request","duration":2698204,"timestamp":1127675453740,"id":5,"tags":{"url":"/"},"startTime":1753009328509,"traceId":"7029ce625923b151"},{"name":"memory-usage","duration":24,"timestamp":1127678152108,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"437653504","memory.heapUsed":"80038200","memory.heapTotal":"117010432"},"startTime":1753009331208,"traceId":"7029ce625923b151"},{"name":"ensure-page","duration":1496,"timestamp":1127678163607,"id":10,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009331219,"traceId":"7029ce625923b151"},{"name":"ensure-page","duration":650,"timestamp":1127678165450,"id":11,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009331221,"traceId":"7029ce625923b151"},{"name":"ensure-page","duration":896,"timestamp":1127678173835,"id":12,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009331229,"traceId":"7029ce625923b151"},{"name":"ensure-page","duration":702,"timestamp":1127678174874,"id":13,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009331231,"traceId":"7029ce625923b151"},{"name":"compile-path","duration":852627,"timestamp":1127678182384,"id":16,"tags":{"trigger":"/_not-found/page"},"startTime":1753009331238,"traceId":"7029ce625923b151"}]
[{"name":"ensure-page","duration":52791,"timestamp":1127679036514,"id":17,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753009332092,"traceId":"7029ce625923b151"},{"name":"handle-request","duration":984425,"timestamp":1127678177203,"id":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009331233,"traceId":"7029ce625923b151"},{"name":"memory-usage","duration":20,"timestamp":1127679161732,"id":18,"parentId":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"484339712","memory.heapUsed":"95202936","memory.heapTotal":"118321152"},"startTime":1753009332217,"traceId":"7029ce625923b151"},{"name":"compile-path","duration":737428,"timestamp":1127694550466,"id":21,"tags":{"trigger":"/api/consultation"},"startTime":1753009347606,"traceId":"7029ce625923b151"}]
[{"name":"hot-reloader","duration":54,"timestamp":1128276527308,"id":3,"tags":{"version":"15.3.5"},"startTime":1753009929592,"traceId":"86aed74dbb6ad1d2"},{"name":"setup-dev-bundler","duration":558754,"timestamp":1128276350941,"id":2,"parentId":1,"tags":{},"startTime":1753009929416,"traceId":"86aed74dbb6ad1d2"},{"name":"run-instrumentation-hook","duration":32,"timestamp":1128276951686,"id":4,"parentId":1,"tags":{},"startTime":1753009930017,"traceId":"86aed74dbb6ad1d2"},{"name":"start-dev-server","duration":1039350,"timestamp":1128275921601,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18169880576","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"177012736","memory.heapTotal":"96104448","memory.heapUsed":"70579592"},"startTime":1753009928987,"traceId":"86aed74dbb6ad1d2"},{"name":"compile-path","duration":1671888,"timestamp":1128285603790,"id":7,"tags":{"trigger":"/"},"startTime":1753009938669,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":1673527,"timestamp":1128285602827,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753009938668,"traceId":"86aed74dbb6ad1d2"}]
[{"name":"ensure-page","duration":30582,"timestamp":1128287280623,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753009940346,"traceId":"86aed74dbb6ad1d2"},{"name":"handle-request","duration":2166445,"timestamp":1128285592811,"id":5,"tags":{"url":"/"},"startTime":1753009938658,"traceId":"86aed74dbb6ad1d2"},{"name":"memory-usage","duration":21,"timestamp":1128287759435,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"437510144","memory.heapUsed":"79558456","memory.heapTotal":"117272576"},"startTime":1753009940825,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":4257,"timestamp":1128287769080,"id":10,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009940834,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":619,"timestamp":1128287773549,"id":11,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009940839,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":600,"timestamp":1128287779114,"id":12,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009940844,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":628,"timestamp":1128287779813,"id":13,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753009940845,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":850907,"timestamp":1128287783165,"id":15,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753009940848,"traceId":"86aed74dbb6ad1d2"},{"name":"ensure-page","duration":67730,"timestamp":1128288635796,"id":19,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753009941701,"traceId":"86aed74dbb6ad1d2"},{"name":"compile-path","duration":1428659,"timestamp":1128287785496,"id":16,"tags":{"trigger":"/_not-found/page"},"startTime":1753009940851,"traceId":"86aed74dbb6ad1d2"}]
[{"name":"ensure-page","duration":53823,"timestamp":1128289223349,"id":20,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1753009942288,"traceId":"86aed74dbb6ad1d2"},{"name":"handle-request","duration":1065479,"timestamp":1128288525042,"id":17,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1753009941590,"traceId":"86aed74dbb6ad1d2"},{"name":"memory-usage","duration":15,"timestamp":1128289590724,"id":21,"parentId":17,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"528850944","memory.heapUsed":"96096736","memory.heapTotal":"122658816"},"startTime":1753009942656,"traceId":"86aed74dbb6ad1d2"},{"name":"compile-path","duration":718265,"timestamp":1128307920904,"id":24,"tags":{"trigger":"/api/consultation"},"startTime":1753009960986,"traceId":"86aed74dbb6ad1d2"}]
[{"name":"next-dev","duration":983288414,"timestamp":1128275212495,"id":1,"tags":{},"startTime":1753009928277,"traceId":"86aed74dbb6ad1d2"}]
[{"name":"next-dev","duration":694015,"timestamp":1130232461332,"id":1,"tags":{},"startTime":1753011885545,"traceId":"35b6e16d4af838ef"}]
[{"name":"hot-reloader","duration":52,"timestamp":1130196166769,"id":3,"tags":{"version":"15.3.5"},"startTime":1753011849250,"traceId":"4a3ffdb0b677c045"},{"name":"setup-dev-bundler","duration":526951,"timestamp":1130196008644,"id":2,"parentId":1,"tags":{},"startTime":1753011849092,"traceId":"4a3ffdb0b677c045"},{"name":"run-instrumentation-hook","duration":30,"timestamp":1130196590904,"id":4,"parentId":1,"tags":{},"startTime":1753011849674,"traceId":"4a3ffdb0b677c045"},{"name":"start-dev-server","duration":1008967,"timestamp":1130195598288,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18439467008","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"179118080","memory.heapTotal":"102658048","memory.heapUsed":"63720392"},"startTime":1753011848682,"traceId":"4a3ffdb0b677c045"},{"name":"compile-path","duration":2180791,"timestamp":1130255489090,"id":7,"tags":{"trigger":"/"},"startTime":1753011908572,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":2182581,"timestamp":1130255488506,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753011908572,"traceId":"4a3ffdb0b677c045"}]
[{"name":"ensure-page","duration":63126,"timestamp":1130257682724,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753011910767,"traceId":"4a3ffdb0b677c045"},{"name":"handle-request","duration":2829451,"timestamp":1130255482262,"id":5,"tags":{"url":"/"},"startTime":1753011908566,"traceId":"4a3ffdb0b677c045"},{"name":"memory-usage","duration":16,"timestamp":1130258311856,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"424923136","memory.heapUsed":"82974504","memory.heapTotal":"90271744"},"startTime":1753011911396,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":3229,"timestamp":1130258329451,"id":10,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753011911414,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":694,"timestamp":1130258332849,"id":11,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753011911417,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":728,"timestamp":1130258340156,"id":12,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753011911424,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":448,"timestamp":1130258340996,"id":13,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753011911425,"traceId":"4a3ffdb0b677c045"},{"name":"compile-path","duration":840844,"timestamp":1130258347373,"id":16,"tags":{"trigger":"/_not-found/page"},"startTime":1753011911431,"traceId":"4a3ffdb0b677c045"}]
[{"name":"ensure-page","duration":51989,"timestamp":1130259189703,"id":17,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753011912274,"traceId":"4a3ffdb0b677c045"},{"name":"handle-request","duration":970732,"timestamp":1130258342508,"id":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753011911427,"traceId":"4a3ffdb0b677c045"},{"name":"memory-usage","duration":11,"timestamp":1130259313327,"id":18,"parentId":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"473837568","memory.heapUsed":"84580448","memory.heapTotal":"101806080"},"startTime":1753011912397,"traceId":"4a3ffdb0b677c045"},{"name":"compile-path","duration":722023,"timestamp":1130274110340,"id":21,"tags":{"trigger":"/api/consultation"},"startTime":1753011927194,"traceId":"4a3ffdb0b677c045"}]
[{"name":"compile-path","duration":1457522,"timestamp":1130274854394,"id":23,"tags":{"trigger":"/_error"},"startTime":1753011927938,"traceId":"4a3ffdb0b677c045"}]
[{"name":"handle-request","duration":2914133,"timestamp":1130274106014,"id":19,"tags":{"url":"/api/consultation"},"startTime":1753011927190,"traceId":"4a3ffdb0b677c045"},{"name":"memory-usage","duration":11,"timestamp":1130277020260,"id":24,"parentId":19,"tags":{"url":"/api/consultation","memory.rss":"600367104","memory.heapUsed":"87548888","memory.heapTotal":"105934848"},"startTime":1753011930104,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":32241,"timestamp":1130354805424,"id":26,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012007891,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":33499,"timestamp":1130354840274,"id":27,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012007925,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":29524,"timestamp":1130354881069,"id":28,"parentId":3,"tags":{"inputPage":"/_error"},"startTime":1753012007966,"traceId":"4a3ffdb0b677c045"},{"name":"handle-request","duration":114132,"timestamp":1130354803042,"id":25,"tags":{"url":"/"},"startTime":1753012007888,"traceId":"4a3ffdb0b677c045"},{"name":"memory-usage","duration":8,"timestamp":1130354917243,"id":29,"parentId":25,"tags":{"url":"/","memory.rss":"589484032","memory.heapUsed":"85728976","memory.heapTotal":"89640960"},"startTime":1753012008002,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":465,"timestamp":1130354987224,"id":30,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012008072,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":276,"timestamp":1130354987766,"id":31,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012008073,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":205,"timestamp":1130354988491,"id":32,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012008074,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":130,"timestamp":1130354988723,"id":33,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012008074,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":47406,"timestamp":1130354990370,"id":35,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753012008076,"traceId":"4a3ffdb0b677c045"},{"name":"ensure-page","duration":32581,"timestamp":1130355039515,"id":36,"parentId":3,"tags":{"inputPage":"/_error"},"startTime":1753012008125,"traceId":"4a3ffdb0b677c045"},{"name":"handle-request","duration":87932,"timestamp":1130354989303,"id":34,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012008074,"traceId":"4a3ffdb0b677c045"},{"name":"memory-usage","duration":6,"timestamp":1130355077269,"id":37,"parentId":34,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"591319040","memory.heapUsed":"86192072","memory.heapTotal":"91213824"},"startTime":1753012008162,"traceId":"4a3ffdb0b677c045"},{"name":"navigation-to-hydration","duration":1157000,"timestamp":1130354794660,"id":38,"parentId":3,"tags":{"pathname":"/","query":""},"startTime":1753012009041,"traceId":"4a3ffdb0b677c045"},{"name":"compile-path","duration":415393,"timestamp":1130356173972,"id":41,"tags":{"trigger":"/favicon.ico"},"startTime":1753012009259,"traceId":"4a3ffdb0b677c045"}]
[{"name":"next-dev","duration":213137447,"timestamp":1130194899628,"id":1,"tags":{},"startTime":1753011847984,"traceId":"4a3ffdb0b677c045"}]
[{"name":"hot-reloader","duration":48,"timestamp":1130411502128,"id":3,"tags":{"version":"15.3.5"},"startTime":1753012064587,"traceId":"89d2756f734f43ae"},{"name":"setup-dev-bundler","duration":557634,"timestamp":1130411331280,"id":2,"parentId":1,"tags":{},"startTime":1753012064416,"traceId":"89d2756f734f43ae"},{"name":"run-instrumentation-hook","duration":42,"timestamp":1130411948114,"id":4,"parentId":1,"tags":{},"startTime":1753012065033,"traceId":"89d2756f734f43ae"},{"name":"start-dev-server","duration":1055818,"timestamp":1130410908928,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18481135616","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"177934336","memory.heapTotal":"96104448","memory.heapUsed":"70485336"},"startTime":1753012063994,"traceId":"89d2756f734f43ae"},{"name":"compile-path","duration":1628528,"timestamp":1130412006757,"id":7,"tags":{"trigger":"/"},"startTime":1753012065092,"traceId":"89d2756f734f43ae"},{"name":"ensure-page","duration":1629530,"timestamp":1130412006441,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012065091,"traceId":"89d2756f734f43ae"}]
[{"name":"ensure-page","duration":26631,"timestamp":1130413639485,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012066724,"traceId":"89d2756f734f43ae"},{"name":"handle-request","duration":1888034,"timestamp":1130411995929,"id":5,"tags":{"url":"/"},"startTime":1753012065081,"traceId":"89d2756f734f43ae"},{"name":"memory-usage","duration":8,"timestamp":1130413884019,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"466530304","memory.heapUsed":"88322968","memory.heapTotal":"113426432"},"startTime":1753012066969,"traceId":"89d2756f734f43ae"},{"name":"compile-path","duration":341655,"timestamp":1130414519290,"id":12,"tags":{"trigger":"/favicon.ico"},"startTime":1753012067604,"traceId":"89d2756f734f43ae"}]
[{"name":"ensure-page","duration":28896,"timestamp":1130414863706,"id":13,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1753012067949,"traceId":"89d2756f734f43ae"},{"name":"handle-request","duration":637837,"timestamp":1130414516261,"id":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1753012067601,"traceId":"89d2756f734f43ae"},{"name":"memory-usage","duration":4,"timestamp":1130415154143,"id":14,"parentId":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"523177984","memory.heapUsed":"91419544","memory.heapTotal":"121757696"},"startTime":1753012068239,"traceId":"89d2756f734f43ae"},{"name":"ensure-page","duration":1680,"timestamp":1130421459049,"id":15,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012074544,"traceId":"89d2756f734f43ae"},{"name":"ensure-page","duration":452,"timestamp":1130421460879,"id":16,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012074546,"traceId":"89d2756f734f43ae"},{"name":"ensure-page","duration":489,"timestamp":1130421462509,"id":17,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012074547,"traceId":"89d2756f734f43ae"},{"name":"ensure-page","duration":536,"timestamp":1130421463139,"id":18,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012074548,"traceId":"89d2756f734f43ae"},{"name":"compile-path","duration":794499,"timestamp":1130421470087,"id":21,"tags":{"trigger":"/_not-found/page"},"startTime":1753012074555,"traceId":"89d2756f734f43ae"}]
[{"name":"ensure-page","duration":55042,"timestamp":1130422267137,"id":22,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753012075352,"traceId":"89d2756f734f43ae"},{"name":"handle-request","duration":928247,"timestamp":1130421464758,"id":19,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012074550,"traceId":"89d2756f734f43ae"},{"name":"memory-usage","duration":13,"timestamp":1130422393092,"id":23,"parentId":19,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"497266688","memory.heapUsed":"86274512","memory.heapTotal":"93372416"},"startTime":1753012075478,"traceId":"89d2756f734f43ae"},{"name":"compile-path","duration":756056,"timestamp":1130435865884,"id":26,"tags":{"trigger":"/api/consultation"},"startTime":1753012088951,"traceId":"89d2756f734f43ae"}]
[{"name":"compile-path","duration":1572832,"timestamp":1130436639577,"id":28,"tags":{"trigger":"/_error"},"startTime":1753012089724,"traceId":"89d2756f734f43ae"}]
[{"name":"next-dev","duration":50475024,"timestamp":1130410189821,"id":1,"tags":{},"startTime":1753012063268,"traceId":"89d2756f734f43ae"}]
[{"name":"hot-reloader","duration":50,"timestamp":1130464650016,"id":3,"tags":{"version":"15.3.5"},"startTime":1753012117736,"traceId":"bcf90c59fc74a9ec"},{"name":"setup-dev-bundler","duration":535665,"timestamp":1130464486672,"id":2,"parentId":1,"tags":{},"startTime":1753012117573,"traceId":"bcf90c59fc74a9ec"},{"name":"run-instrumentation-hook","duration":33,"timestamp":1130465076222,"id":4,"parentId":1,"tags":{},"startTime":1753012118162,"traceId":"bcf90c59fc74a9ec"},{"name":"start-dev-server","duration":1021165,"timestamp":1130464090380,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18564698112","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"189935616","memory.heapTotal":"100298752","memory.heapUsed":"66547696"},"startTime":1753012117176,"traceId":"bcf90c59fc74a9ec"},{"name":"compile-path","duration":1527414,"timestamp":1130480594278,"id":7,"tags":{"trigger":"/"},"startTime":1753012133680,"traceId":"bcf90c59fc74a9ec"},{"name":"ensure-page","duration":1528977,"timestamp":1130480593414,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012133679,"traceId":"bcf90c59fc74a9ec"}]
[{"name":"ensure-page","duration":26898,"timestamp":1130482127749,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012135214,"traceId":"bcf90c59fc74a9ec"},{"name":"handle-request","duration":1818376,"timestamp":1130480587017,"id":5,"tags":{"url":"/"},"startTime":1753012133673,"traceId":"bcf90c59fc74a9ec"},{"name":"memory-usage","duration":16,"timestamp":1130482405547,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"449257472","memory.heapUsed":"81035504","memory.heapTotal":"114913280"},"startTime":1753012135491,"traceId":"bcf90c59fc74a9ec"},{"name":"ensure-page","duration":1223,"timestamp":1130482430323,"id":10,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012135516,"traceId":"bcf90c59fc74a9ec"},{"name":"ensure-page","duration":197,"timestamp":1130482431633,"id":11,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012135518,"traceId":"bcf90c59fc74a9ec"},{"name":"ensure-page","duration":278,"timestamp":1130482434361,"id":12,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012135520,"traceId":"bcf90c59fc74a9ec"},{"name":"ensure-page","duration":156,"timestamp":1130482434693,"id":13,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012135521,"traceId":"bcf90c59fc74a9ec"},{"name":"compile-path","duration":477526,"timestamp":1130482438612,"id":16,"tags":{"trigger":"/_not-found/page"},"startTime":1753012135525,"traceId":"bcf90c59fc74a9ec"}]
[{"name":"ensure-page","duration":27319,"timestamp":1130482917269,"id":17,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753012136003,"traceId":"bcf90c59fc74a9ec"},{"name":"handle-request","duration":544442,"timestamp":1130482435465,"id":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012135521,"traceId":"bcf90c59fc74a9ec"},{"name":"memory-usage","duration":3,"timestamp":1130482979943,"id":18,"parentId":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"477306880","memory.heapUsed":"88891168","memory.heapTotal":"117010432"},"startTime":1753012136066,"traceId":"bcf90c59fc74a9ec"},{"name":"compile-path","duration":757930,"timestamp":1130498503033,"id":21,"tags":{"trigger":"/api/consultation"},"startTime":1753012151589,"traceId":"bcf90c59fc74a9ec"}]
[{"name":"ensure-page","duration":57747,"timestamp":1130499266245,"id":22,"parentId":3,"tags":{"inputPage":"/api/consultation/route"},"startTime":1753012152352,"traceId":"bcf90c59fc74a9ec"},{"name":"compile-path","duration":1484990,"timestamp":1130499622872,"id":24,"tags":{"trigger":"/_error"},"startTime":1753012152709,"traceId":"bcf90c59fc74a9ec"}]
[{"name":"next-dev","duration":147870596,"timestamp":1130463464096,"id":1,"tags":{},"startTime":1753012116535,"traceId":"bcf90c59fc74a9ec"}]
[{"name":"hot-reloader","duration":56,"timestamp":1130615568423,"id":3,"tags":{"version":"15.3.5"},"startTime":1753012268644,"traceId":"5c6af57bceea77a2"},{"name":"setup-dev-bundler","duration":567672,"timestamp":1130615388880,"id":2,"parentId":1,"tags":{},"startTime":1753012268464,"traceId":"5c6af57bceea77a2"},{"name":"run-instrumentation-hook","duration":41,"timestamp":1130615997481,"id":4,"parentId":1,"tags":{},"startTime":1753012269073,"traceId":"5c6af57bceea77a2"},{"name":"start-dev-server","duration":1167510,"timestamp":1130614839918,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18420580352","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"177655808","memory.heapTotal":"96104448","memory.heapUsed":"70435984"},"startTime":1753012267915,"traceId":"5c6af57bceea77a2"},{"name":"compile-path","duration":1578868,"timestamp":1130619032321,"id":7,"tags":{"trigger":"/"},"startTime":1753012272108,"traceId":"5c6af57bceea77a2"},{"name":"ensure-page","duration":1579763,"timestamp":1130619032048,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012272107,"traceId":"5c6af57bceea77a2"}]
[{"name":"ensure-page","duration":23228,"timestamp":1130620615700,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012273691,"traceId":"5c6af57bceea77a2"},{"name":"handle-request","duration":1855078,"timestamp":1130619025180,"id":5,"tags":{"url":"/"},"startTime":1753012272100,"traceId":"5c6af57bceea77a2"},{"name":"memory-usage","duration":18,"timestamp":1130620880430,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"462245888","memory.heapUsed":"80142112","memory.heapTotal":"118321152"},"startTime":1753012273956,"traceId":"5c6af57bceea77a2"},{"name":"ensure-page","duration":1294,"timestamp":1130620901546,"id":10,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012273977,"traceId":"5c6af57bceea77a2"},{"name":"ensure-page","duration":425,"timestamp":1130620903120,"id":11,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012273978,"traceId":"5c6af57bceea77a2"},{"name":"ensure-page","duration":1041,"timestamp":1130620905209,"id":12,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012273980,"traceId":"5c6af57bceea77a2"},{"name":"ensure-page","duration":500,"timestamp":1130620906384,"id":13,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012273982,"traceId":"5c6af57bceea77a2"},{"name":"compile-path","duration":647570,"timestamp":1130620911676,"id":16,"tags":{"trigger":"/_not-found/page"},"startTime":1753012273987,"traceId":"5c6af57bceea77a2"}]
[{"name":"ensure-page","duration":61129,"timestamp":1130621560720,"id":17,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1753012274636,"traceId":"5c6af57bceea77a2"},{"name":"handle-request","duration":767234,"timestamp":1130620907851,"id":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1753012273983,"traceId":"5c6af57bceea77a2"},{"name":"memory-usage","duration":11,"timestamp":1130621675148,"id":18,"parentId":14,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"503549952","memory.heapUsed":"94861672","memory.heapTotal":"118845440"},"startTime":1753012274750,"traceId":"5c6af57bceea77a2"},{"name":"compile-path","duration":742967,"timestamp":1130637666194,"id":21,"tags":{"trigger":"/api/consultation"},"startTime":1753012290741,"traceId":"5c6af57bceea77a2"}]
[{"name":"compile-path","duration":1421147,"timestamp":1130638428806,"id":23,"tags":{"trigger":"/_error"},"startTime":1753012291504,"traceId":"5c6af57bceea77a2"}]
[{"name":"next-dev","duration":86627951,"timestamp":1130614159191,"id":1,"tags":{},"startTime":1753012267234,"traceId":"5c6af57bceea77a2"}]
[{"name":"hot-reloader","duration":47,"timestamp":1130704144472,"id":3,"tags":{"version":"15.3.5"},"startTime":1753012357223,"traceId":"34ba2ea4f2dc1143"},{"name":"setup-dev-bundler","duration":530740,"timestamp":1130703985197,"id":2,"parentId":1,"tags":{},"startTime":1753012357064,"traceId":"34ba2ea4f2dc1143"},{"name":"run-instrumentation-hook","duration":20,"timestamp":1130704552982,"id":4,"parentId":1,"tags":{},"startTime":1753012357632,"traceId":"34ba2ea4f2dc1143"},{"name":"start-dev-server","duration":968892,"timestamp":1130703594845,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"18490593280","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"177180672","memory.heapTotal":"96104448","memory.heapUsed":"70504584"},"startTime":1753012356673,"traceId":"34ba2ea4f2dc1143"},{"name":"compile-path","duration":1467399,"timestamp":1130708588194,"id":7,"tags":{"trigger":"/"},"startTime":1753012361667,"traceId":"34ba2ea4f2dc1143"},{"name":"ensure-page","duration":1468362,"timestamp":1130708587925,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012361666,"traceId":"34ba2ea4f2dc1143"}]
[{"name":"ensure-page","duration":30801,"timestamp":1130710060073,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1753012363139,"traceId":"34ba2ea4f2dc1143"},{"name":"handle-request","duration":1766466,"timestamp":1130708582508,"id":5,"tags":{"url":"/"},"startTime":1753012361661,"traceId":"34ba2ea4f2dc1143"},{"name":"memory-usage","duration":72,"timestamp":1130710349055,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"463970304","memory.heapUsed":"84402112","memory.heapTotal":"121057280"},"startTime":1753012363428,"traceId":"34ba2ea4f2dc1143"},{"name":"compile-path","duration":758021,"timestamp":1130724603643,"id":12,"tags":{"trigger":"/api/consultation"},"startTime":1753012377682,"traceId":"34ba2ea4f2dc1143"}]
[{"name":"next-dev","duration":779910,"timestamp":1131279700078,"id":1,"tags":{},"startTime":1753012932790,"traceId":"db4e62d267bc50b9"}]
[{"name":"hot-reloader","duration":63,"timestamp":1131332613011,"id":3,"tags":{"version":"15.3.5"},"startTime":1753012985704,"traceId":"979926e898e01c3b"},{"name":"setup-dev-bundler","duration":609592,"timestamp":1131332423413,"id":2,"parentId":1,"tags":{},"startTime":1753012985514,"traceId":"979926e898e01c3b"},{"name":"run-instrumentation-hook","duration":22,"timestamp":1131333082384,"id":4,"parentId":1,"tags":{},"startTime":1753012986173,"traceId":"979926e898e01c3b"},{"name":"start-dev-server","duration":1147995,"timestamp":1131331957176,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"17569095680","memory.totalMem":"33974333440","memory.heapSizeLimit":"17037262848","memory.rss":"180989952","memory.heapTotal":"96628736","memory.heapUsed":"70957824"},"startTime":1753012985048,"traceId":"979926e898e01c3b"},{"name":"compile-path","duration":594784,"timestamp":1131359837496,"id":7,"tags":{"trigger":"/api/consultation"},"startTime":1753013012928,"traceId":"979926e898e01c3b"},{"name":"ensure-page","duration":596070,"timestamp":1131359836915,"id":6,"parentId":3,"tags":{"inputPage":"/api/consultation/route"},"startTime":1753013012927,"traceId":"979926e898e01c3b"}]
