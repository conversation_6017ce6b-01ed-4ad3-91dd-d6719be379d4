// Simple test to verify our API logic
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.production' });

console.log('Environment variables loaded:');
console.log('MONGODB_URI:', process.env.MONGODB_URI ? 'Set' : 'Not set');
console.log('BREVO_API_KEY:', process.env.BREVO_API_KEY ? 'Set' : 'Not set');

// Test data
const testData = {
  name: "Test User",
  email: "<EMAIL>",
  phone: "1234567890",
  propertyAddress: "123 Test St",
  inquiryType: "sell",
  message: "This is a test message"
};

console.log('Test data:', testData);
console.log('API should work with this configuration.');
