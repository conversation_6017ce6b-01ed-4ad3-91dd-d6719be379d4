{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/app/api/consultation/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\n\r\nconst BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5001';\r\n\r\n// POST - Create a new consultation request (proxy to backend)\r\nexport async function POST(request) {\r\n  try {\r\n    const body = await request.json();\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/consultation`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(body),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    return NextResponse.json(data, { status: response.status });\r\n\r\n  } catch (error) {\r\n    console.error('Error proxying consultation request:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to process request. Please try again later.'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// GET - Retrieve consultation requests (proxy to backend)\r\nexport async function GET(request) {\r\n  try {\r\n    const { searchParams } = new URL(request.url);\r\n    const queryString = searchParams.toString();\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/consultation?${queryString}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    return NextResponse.json(data, { status: response.status });\r\n\r\n  } catch (error) {\r\n    console.error('Error proxying consultation GET request:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Internal server error'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PATCH - Update consultation status (proxy to backend)\r\nexport async function PATCH(request) {\r\n  try {\r\n    const body = await request.json();\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/consultation`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(body),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    return NextResponse.json(data, { status: response.status });\r\n\r\n  } catch (error) {\r\n    console.error('Error proxying consultation PATCH request:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Internal server error'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAGxC,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,iBAAiB,CAAC,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAE3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,aAAa,QAAQ;QAEzC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,kBAAkB,EAAE,aAAa,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAE3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MAAM,OAAO;IACjC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,iBAAiB,CAAC,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAE3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}